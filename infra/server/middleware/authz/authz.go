package middleware

import (
	"context"

	"github.com/google/uuid"

	"gitlab.viswalslab.com/backend/price-list/core"
)

type contextKey string

const principalKey contextKey = "authz_principal"

type Principal struct {
	AccountID uuid.UUID
	BranchID  uuid.UUID
	Role      string
}

func WithPrincipal(ctx context.Context, p Principal) context.Context {
	return context.WithValue(ctx, principalKey, p)
}

func FromContext(ctx context.Context) (Principal, bool) {
	val := ctx.Value(principalKey)
	p, ok := val.(Principal)
	return p, ok
}

// RequireRolesForBranch ensures that the user has at least one of the required roles
func RequireRolesForBranch(ctx context.Context, required ...string) *AdminInfo {
	p, ok := FromContext(ctx)
	if !ok || p.AccountID == uuid.Nil {
		return nil
	}
	if p.BranchID == uuid.Nil {
		return nil
	}
	if !core.HasAnyRole(p.Role, required...) {
		return nil
	}

	return &AdminInfo{
		AccountID: p.AccountID,
		BranchID:  p.BranchID,
		Role:      p.Role,
	}
}

type AdminInfo struct {
	AccountID uuid.UUID
	BranchID  uuid.UUID
	Role      string
}
