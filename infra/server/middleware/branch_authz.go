package middleware

import (
  "encoding/json"
  "fmt"
  "net/http"
  "strings"

  "github.com/golang-jwt/jwt"
  "github.com/google/uuid"
  "github.com/labstack/echo/v4"
  authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
)

// BranchAuthzMiddleware extracts account_id and branch_role from JWT, branch_id from request, and injects Principal into context.
func BranchAuthzMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
  return func(c echo.Context) error {
    header := c.Request().Header.Get("Authorization")
    if header == "" {
      return c.JSON(http.StatusUnauthorized, "Unauthorized")
    }
    tokenString := strings.TrimSpace(strings.TrimPrefix(header, "Bearer "))
    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: invalid token")
    }
    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: invalid claims")
    }
    accountIDStr, ok := claims["account_id"].(string)
    if !ok || accountIDStr == "" {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: missing account_id")
    }
    accountID, err := uuid.Parse(accountIDStr)
    if err != nil {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: invalid account_id")
    }
    branchRolesRaw, ok := claims["branch_role"]
    if !ok {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: missing branch_role")
    }
    branchRoles := map[string]string{}
    branchRolesBytes, _ := json.Marshal(branchRolesRaw)
    _ = json.Unmarshal(branchRolesBytes, &branchRoles)

    branchIDStr := c.Param("branch_id")
    fmt.Println("Branch ID from URL:", branchIDStr)
    if branchIDStr == "" {
      return c.JSON(http.StatusBadRequest, "Invalid branch ID")
    }
    branchID, err := uuid.Parse(branchIDStr)
    if err != nil {
      return c.JSON(http.StatusBadRequest, "Invalid branch ID format")
    }

    role, ok := branchRoles[branchIDStr]
    if !ok {
      return c.JSON(http.StatusUnauthorized, "Unauthorized: no role for branch")
    }

    ctx := c.Request().Context()
    ctx = authz.WithPrincipal(ctx, authz.Principal{
      AccountID: accountID,
      BranchID:  branchID,
      Role:      role,
    })
    c.SetRequest(c.Request().WithContext(ctx))
    return next(c)
  }
}