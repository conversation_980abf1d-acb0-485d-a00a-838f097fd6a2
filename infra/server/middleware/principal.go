package middleware

import (
	"context"
	"net/http"

	"github.com/google/uuid"

	"gitlab.viswalslab.com/backend/price-list/core"
	authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
)

type contextKey string

const (
	UserIDKey contextKey = "userID"
	RoleKey   contextKey = "role"
)

func WithUserID(ctx context.Context, userID uuid.UUID) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

func WithRole(ctx context.Context, role string) context.Context {
	return context.WithValue(ctx, <PERSON>Key, role)
}

func GetUserID(ctx context.Context) (uuid.UUID, bool) {
	userID, ok := ctx.Value(UserIDKey).(uuid.UUID)
	return userID, ok
}

func GetRole(ctx context.Context) (string, bool) {
	role, ok := ctx.Value(RoleKey).(string)
	return role, ok
}

type AuthManager interface {
	GetUserUUID(tokenString string) (*uuid.UUID, error)
}

type RoleRepository interface {
	GetUserRole(ctx context.Context, userID, branchID uuid.UUID) (string, error)
}

func Middleware(authManager AuthManager, roleRepo RoleRepository) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

			header := r.Header.Get("Authorization")
			if header == "" {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}

			userIDPtr, err := authManager.GetUserUUID(header)
			if err != nil || userIDPtr == nil {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}

			userID := *userIDPtr

			branchIDParam := r.URL.Query().Get("branch")
			if branchIDParam == "" {
				http.Error(w, "Invalid branch ID", http.StatusBadRequest)
				return
			}

			branchID, err := uuid.Parse(branchIDParam)
			if err != nil {
				http.Error(w, "Invalid branch ID format", http.StatusBadRequest)
				return
			}

			userRole, err := roleRepo.GetUserRole(r.Context(), userID, branchID)
			if err != nil {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}

			ctx := r.Context()
			ctx = WithUserID(ctx, userID)
			ctx = WithRole(ctx, mapRoleToCore(userRole))

			ctx = authz.WithPrincipal(r.Context(), authz.Principal{
				AccountID: userID,
				BranchID:  branchID,
				Role:      mapRoleToCore(userRole),
			})
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func mapRoleToCore(dbRole string) string {
	switch dbRole {
	case "owner":
		return core.RoleBranchOwner
	case "super-admin":
		return core.RoleSuperAdmin
	case "member":
		return core.RoleMember
	default:
		return dbRole
	}
}