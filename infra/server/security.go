package server

import (
	"fmt"
	"strings"

	"github.com/labstack/echo/v4"
	echoMiddleware "github.com/labstack/echo/v4/middleware"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
)

func BootstrapSecurity(router *echo.Echo, cfg config.Configuration) {
	router.Use(echoMiddleware.CORSWithConfig(echoMiddleware.CORSConfig{
		AllowOrigins:     cfg.Security.AllowOrigins,
		AllowMethods:     cfg.Security.AllowMethods,
		AllowHeaders:     cfg.Security.AllowHeaders,
		AllowCredentials: cfg.Security.AllowCredentials,
	}))

	router.Use(echoMiddleware.SecureWithConfig(echoMiddleware.SecureConfig{
		XSSProtection:         "1; mode=block",
		ContentTypeNosniff:    "nosniff",
		XFrameOptions:         "DENY",
		HSTSMaxAge:            63072000, // 2 years
		ContentSecurityPolicy: "default-src 'self'",
	}))

	// Debug: Log the MaxBodySize value being used
	fmt.Printf("DEBUG: Applying BodyLimit middleware with MaxBodySize: '%s'\n", cfg.Security.MaxBodySize)
	router.Use(echoMiddleware.BodyLimit(cfg.Security.MaxBodySize)) // adjust size as needed

	router.Use(echoMiddleware.RateLimiter(
		echoMiddleware.NewRateLimiterMemoryStore(cfg.Security.RateLimitPerSecond), // X requests/sec/IP
	))

	if strings.ToLower(cfg.Environment) == "production" {
		router.Pre(echoMiddleware.HTTPSRedirect())
	}
}
