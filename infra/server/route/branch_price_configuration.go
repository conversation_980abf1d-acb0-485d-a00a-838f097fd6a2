package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	authmw "gitlab.viswalslab.com/backend/price-list/infra/server/middleware"
	branchauthz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/transport"
)

type BranchPriceConfigurationFactory interface {
	CreateBranchPriceConfigurationHandler() *transport.CreateBranchPriceConfigurationHandler
	GetBranchPriceConfigurationHandler() *transport.GetBranchPriceConfigurationHandler
}

type BranchPriceConfigurationRouter struct {
	baseRoute *echo.Group
	factory   BranchPriceConfigurationFactory
}

func NewBranchPriceConfigurationRouter(e *echo.Group, factory BranchPriceConfigurationFactory) *BranchPriceConfigurationRouter {
	return &BranchPriceConfigurationRouter{
		baseRoute: e,
		factory:   factory,
	}
}

func (r *BranchPriceConfigurationRouter) Route() {
	createBFNHandler := r.factory.CreateBranchPriceConfigurationHandler()
	getBFNHandler := r.factory.GetBranchPriceConfigurationHandler()
	group := r.baseRoute.Group("/branch-price-configurations", authmw.CognitoJWT(config.FromEnv()))
	group.POST("/:branch_id", createBFNHandler.Handle(), branchauthz.BranchAuthzMiddleware)
	group.GET("/:branch_id", getBFNHandler.Handle(), branchauthz.BranchAuthzMiddleware)
}
