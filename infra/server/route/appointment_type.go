package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

type AppointmentTypeFactory interface {
	ListAppointmentTypeHandler() *transport.ListAppointmentTypesHandler
	GetAppointmentTypeHandler() *transport.GetAppointmentTypeHandler
}

type appointmentTypeRouter struct {
	baseRoute *echo.Group
	factory   AppointmentTypeFactory
}

func NewAppointmentTypeRouter(e *echo.Group, factory AppointmentTypeFactory) *appointmentTypeRouter {
	return &appointmentTypeRouter{
		baseRoute: e,
		factory:   factory,
	}
}

func (r *appointmentTypeRouter) Route() {
	listHandler := r.factory.ListAppointmentTypeHandler()
	getHandler := r.factory.GetAppointmentTypeHandler()

	group := r.baseRoute.Group("/appointment-types")
	group.GET("", listHandler.Handle())
	group.GET("/:id", getHandler.Handle())
}
