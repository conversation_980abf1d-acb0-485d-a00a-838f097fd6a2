package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

type AreaFactory interface {
	ListAreaHandler() *transport.ListAreasHandler
	GetAreaHandler() *transport.GetAreaHandler
}

type areaRouter struct {
	baseRoute *echo.Group
	factory   AreaFactory
}

func NewAreaRouter(e *echo.Group, factory AreaFactory) *areaRouter {
	return &areaRouter{
		baseRoute: e,
		factory:   factory,
	}
}

func (r *areaRouter) Route() {
	listHandler := r.factory.ListAreaHandler()
	getHandler := r.factory.GetAreaHandler()

	group := r.baseRoute.Group("/areas")
	group.GET("", listHandler.Handle())
	group.GET("/:id", getHandler.Handle())
}
