package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
)

type FieldNatureFactory interface {
	ListFieldNaturesHandler() *transport.ListFieldNaturesHandler
	GetFieldNatureHandler() *transport.GetFieldNatureHandler
}

type fieldNatureRouter struct {
	baseRoute *echo.Group
	factory   FieldNatureFactory
}

func NewFieldNatureRouter(e *echo.Group, factory FieldNatureFactory) *fieldNatureRouter {
	return &fieldNatureRouter{
		baseRoute: e,
		factory:   factory,
	}
}



func (r *fieldNatureRouter) Route() {
	listHandler := r.factory.ListFieldNaturesHandler()
	getHandler := r.factory.GetFieldNatureHandler()

	group := r.baseRoute.Group("/fields")
	group.GET("", listHandler.Handle())
	group.GET("/:id", getHandler.Handle())
}
