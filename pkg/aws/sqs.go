package aws

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

// SQSClient represents the SQS client configuration
type SQSClient struct {
	region          string
	accessKeyID     string
	secretAccessKey string
	queueURL        string
	client          *sqs.Client
}

// SQSConfig contains the configuration for SQS client
type SQSConfig struct {
	Region          string
	AccessKeyID     string
	SecretAccessKey string
	QueueURL        string
}

// SQSMessage represents a message received from SQS
type SQSMessage struct {
	MessageID string
	Body      string
}

// NewSQSClient creates a new SQS client instance
func NewSQSClient(cfg SQSConfig) (*SQSClient, error) {
	if cfg.Region == "" {
		return nil, fmt.Errorf("AWS region is required")
	}
	if cfg.AccessKeyID == "" {
		return nil, fmt.Errorf("AWS access key ID is required")
	}
	if cfg.SecretAccessKey == "" {
		return nil, fmt.Errorf("AWS secret access key is required")
	}
	if cfg.QueueURL == "" {
		return nil, fmt.Errorf("SQS queue URL is required")
	}

	return &SQSClient{
		region:          cfg.Region,
		accessKeyID:     cfg.AccessKeyID,
		secretAccessKey: cfg.SecretAccessKey,
		queueURL:        cfg.QueueURL,
	}, nil
}

// Connect establishes connection to AWS SQS
func (s *SQSClient) Connect(ctx context.Context) (*sqs.Client, error) {
	// Create AWS config with credentials
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(s.region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			s.accessKeyID,
			s.secretAccessKey,
			"", // session token (empty for static credentials)
		)),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	// Create SQS client
	sqsClient := sqs.NewFromConfig(cfg)

	// Test connection by getting queue attributes
	_, err = sqsClient.GetQueueAttributes(ctx, &sqs.GetQueueAttributesInput{
		QueueUrl: aws.String(s.queueURL),
		AttributeNames: []types.QueueAttributeName{
			types.QueueAttributeNameQueueArn,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to SQS queue: %w", err)
	}

	s.client = sqsClient
	return sqsClient, nil
}

// SendMessage sends a message to the SQS queue
func (s *SQSClient) SendMessage(ctx context.Context, messageBody string, messageAttributes map[string]string) (*sqs.SendMessageOutput, error) {
	if s.client == nil {
		return nil, fmt.Errorf("SQS client not connected")
	}

	input := &sqs.SendMessageInput{
		QueueUrl:    aws.String(s.queueURL),
		MessageBody: aws.String(messageBody),
	}

	// Add message attributes if provided
	if len(messageAttributes) > 0 {
		attrs := make(map[string]types.MessageAttributeValue)
		for key, value := range messageAttributes {
			attrs[key] = types.MessageAttributeValue{
				DataType:    aws.String("String"),
				StringValue: aws.String(value),
			}
		}
		input.MessageAttributes = attrs
	}

	return s.client.SendMessage(ctx, input)
}

// ReceiveMessages receives messages from the SQS queue
func (s *SQSClient) ReceiveMessages(ctx context.Context, maxMessages int32, waitTimeSeconds int32) (*sqs.ReceiveMessageOutput, error) {
	if s.client == nil {
		return nil, fmt.Errorf("SQS client not connected")
	}

	input := &sqs.ReceiveMessageInput{
		QueueUrl:            aws.String(s.queueURL),
		MaxNumberOfMessages: maxMessages,
		WaitTimeSeconds:     waitTimeSeconds,
		MessageAttributeNames: []string{
			"All",
		},
	}

	return s.client.ReceiveMessage(ctx, input)
}

// Close performs cleanup if needed (SQS doesn't require explicit closing)
func (s *SQSClient) Close() error {
	s.client = nil
	return nil
}

// DeleteMessage deletes a message from the SQS queue
func (s *SQSClient) DeleteMessage(ctx context.Context, receiptHandle string) (*sqs.DeleteMessageOutput, error) {
	if s.client == nil {
		return nil, fmt.Errorf("SQS client not connected")
	}

	input := &sqs.DeleteMessageInput{
		QueueUrl:      aws.String(s.queueURL),
		ReceiptHandle: aws.String(receiptHandle),
	}

	return s.client.DeleteMessage(ctx, input)
}

// GetClient returns the SQS client instance
func (s *SQSClient) GetClient() *sqs.Client {
	return s.client
}

// GetQueueURL returns the configured queue URL
func (s *SQSClient) GetQueueURL() string {
	return s.queueURL
}

// InitializeSQSClient creates and connects a new SQS client with the provided configuration
func InitializeSQSClient(sqsConfig SQSConfig) (*SQSClient, error) {
	// Create SQS client
	sqsClient, err := NewSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create SQS client: %w", err)
	}

	// Connect to AWS SQS
	ctx := context.Background()
	_, err = sqsClient.Connect(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to AWS SQS: %w", err)
	}

	return sqsClient, nil
}
