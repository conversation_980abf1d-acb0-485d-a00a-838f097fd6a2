package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func derefString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

type natureRow struct {
	UUID     string  `db:"uuid"`
	Name     string  `db:"name"`
	Icon     *string `db:"icon"`
	Enabled  bool    `db:"enabled"`
}

func (r *FieldNatureRepository) GetNaturesByFieldUUID(ctx context.Context, fieldUUID string) ([]*entity.Nature, error) {
	query := `SELECT n.uuid, n.name, n.icon, n.enabled
              FROM nature n
              JOIN field_nature fn ON n.uuid = fn.nature_uuid
              WHERE fn.field_uuid = $1`
	var rows []natureRow
	if err := r.db.SelectContext(ctx, &rows, query, fieldUUID); err != nil {
		r.logger.With(vlog.F("method", "GetNaturesByFieldUUID"), vlog.F("action", "select natures by field UUID")).Error(err.Error())
		return nil, err
	}
	result := make([]*entity.Nature, len(rows))
	for i, n := range rows {
		nature, err := entity.NewNature(&entity.NewNatureInput{
			UUID:    n.UUID,
			Name:    n.Name,
			Icon:    derefString(n.Icon),
			Enabled: n.Enabled,
		})
		if err != nil {
			r.logger.With(vlog.F("method", "GetNaturesByFieldUUID"), vlog.F("action", "construct Nature entity")).Error(err.Error())
			return nil, err
		}
		result[i] = nature
	}
	return result, nil
}
