package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestFieldNatureRepository_GetNaturesByFieldUUID_Success(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()
	natureUUID1 := core.NewID().Value()
	natureUUID2 := core.NewID().Value()

	mock.ExpectQuery("SELECT n.uuid, n.name, n.icon, n.enabled").
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow(natureUUID1, "Nature 1", "Icon1", true).
			AddRow(natureUUID2, "Nature 2", "Icon2", false))

	natures, err := repo.GetNaturesByFieldUUID(ctx, fieldUUID)
	assert.NoError(t, err)
	assert.Len(t, natures, 2)
	assert.Equal(t, "Nature 1", natures[0].Name())
	assert.Equal(t, "Nature 2", natures[1].Name())
	assert.True(t, natures[0].Enabled())
	assert.False(t, natures[1].Enabled())
}

func TestFieldNatureRepository_GetNaturesByFieldUUID_DBError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()

	mock.ExpectQuery("SELECT n.uuid, n.name, n.icon, n.enabled").
		WithArgs(fieldUUID).
		WillReturnError(sql.ErrConnDone)

	_, err := repo.GetNaturesByFieldUUID(ctx, fieldUUID)
	assert.Error(t, err)
}

func TestFieldNatureRepository_GetNaturesByFieldUUID_InvalidUUID(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()

	mock.ExpectQuery("SELECT n.uuid, n.name, n.icon, n.enabled").
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("not-a-uuid", "Nature 1", "Icon1", true))

	_, err := repo.GetNaturesByFieldUUID(ctx, fieldUUID)
	assert.Error(t, err)
}
