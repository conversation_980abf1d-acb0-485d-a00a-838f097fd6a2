package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type FieldNatureRepository struct {
	db     *sqlx.DB
	logger vlog.Logger
}

type ListFieldNaturesFilter struct {
	Page      int
	Size      int
	Enabled   *bool
	Name      *string
	AreaUUID  *string
	OrderBy   string
	Direction string
}

type fieldRow struct {
	UUID      string    `db:"uuid"`
	Name      *string   `db:"name"`
	Icon      *string   `db:"icon"`
	Position  *int      `db:"position"`
	Enabled   bool      `db:"enabled"`
	AreaUUID  string    `db:"area_uuid"`
	CreatedAt time.Time `db:"created_at"`
	UpdatedAt time.Time `db:"updated_at"`
}

func derefInt(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

func NewFieldNatureRepository(db *sqlx.DB, logger vlog.Logger) *FieldNatureRepository {
	return &FieldNatureRepository{db: db, logger: logger}
}

func (r *FieldNatureRepository) ListFieldNatures(ctx context.Context, params ListFieldNaturesFilter) ([]*entity.Field, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "ListFieldNatures"), vlog.F("action", "list field natures"))

	var (
		query      string
		countQuery string
		rows       *sqlx.Rows
		err        error
		args       []interface{}
		argIdx     = 1
	)

	query = "SELECT uuid, name, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE 1=1"
	countQuery = "SELECT COUNT(uuid) FROM field WHERE 1=1"

	filterArgs := []interface{}{}
	filterIdx := 1
	if params.Name != nil && *params.Name != "" {
		query += fmt.Sprintf(" AND LOWER(name) LIKE $%d", argIdx)
		countQuery += fmt.Sprintf(" AND LOWER(name) LIKE $%d", filterIdx)
		args = append(args, "%"+strings.ToLower(*params.Name)+"%")
		filterArgs = append(filterArgs, "%"+strings.ToLower(*params.Name)+"%")
		argIdx++
		filterIdx++
	}
	if params.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIdx)
		countQuery += fmt.Sprintf(" AND enabled = $%d", filterIdx)
		args = append(args, *params.Enabled)
		filterArgs = append(filterArgs, *params.Enabled)
		argIdx++
		filterIdx++
	}
	if params.AreaUUID != nil && *params.AreaUUID != "" {
		query += fmt.Sprintf(" AND area_uuid = $%d", argIdx)
		countQuery += fmt.Sprintf(" AND area_uuid = $%d", filterIdx)
		args = append(args, *params.AreaUUID)
		filterArgs = append(filterArgs, *params.AreaUUID)
		argIdx++
		filterIdx++
	}
	orderBy := "name"
	if params.OrderBy != "" {
		orderBy = params.OrderBy
	}
	direction := "ASC"
	if strings.ToUpper(params.Direction) == "DESC" {
		direction = "DESC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, direction)
	if params.Size > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIdx, argIdx+1)
		args = append(args, params.Size)
		offset := 0
		if params.Page > 0 {
			offset = params.Page * params.Size
		}
		args = append(args, offset)
	}

	logger.Debug("executing query", vlog.F("query", query), vlog.F("args", args))
	rows, err = r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("failed to query fields: " + err.Error())
		return nil, 0, err
	}
	defer rows.Close()

	var output []*entity.Field
	for rows.Next() {
		var row fieldRow
		if err := rows.StructScan(&row); err != nil {
			fmt.Printf("failed to struct scan field row: %v\n", err)
			return nil, 0, err
		}
		// Fetch associated natures for this field
		natures, err := r.GetNaturesByFieldUUID(ctx, row.UUID)
		if err != nil {
			r.logger.With(vlog.F("method", "ListFields"), vlog.F("action", "fetch natures by field UUID")).Error(err.Error())
			return nil, 0, err
		}
		natureVals := make([]entity.Nature, len(natures))
		for i, n := range natures {
			natureVals[i] = *n
		}
		input := &entity.NewFieldInput{
			UUID:      row.UUID,
			Name:      derefString(row.Name),
			Icon:      derefString(row.Icon),
			Position:  derefInt(row.Position),
			Enabled:   row.Enabled,
			CreatedAt: &row.CreatedAt,
			UpdatedAt: &row.UpdatedAt,
			AreaUUID:  &row.AreaUUID,
			Natures:   natureVals,
		}
		field, err := entity.NewField(input)
		if err != nil {
			r.logger.With(vlog.F("method", "ListFields"), vlog.F("action", "construct entity from DB")).Error(err.Error())
			return nil, 0, err
		}
		output = append(output, field)
	}

	var total int64
	if err := r.db.GetContext(ctx, &total, countQuery, filterArgs...); err != nil {
		logger.Error("FieldNatureRepository.ListFields: failed to get total count", vlog.F("error", err))
		return nil, 0, err
	}

	return output, total, nil
}
