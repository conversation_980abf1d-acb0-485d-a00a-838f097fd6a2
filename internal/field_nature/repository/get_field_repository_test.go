package repository

import (
	"context"
	"database/sql"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestFieldNatureRepository_GetFieldNature_Success(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()
	areaUUID := core.NewID().Value()
	now := time.Now()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT uuid, name, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "position", "enabled", "area_uuid", "created_at", "updated_at"}).
			AddRow(fieldUUID, "Test Field", "icon", 1, true, areaUUID, now, now))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT n.uuid, n.name, n.icon, n.enabled FROM nature n JOIN field_nature fn ON n.uuid = fn.nature_uuid WHERE fn.field_uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow(core.NewID().Value(), "Nature 1", "Icon1", true).
			AddRow(core.NewID().Value(), "Nature 2", "Icon2", false))

	field, err := repo.GetFieldNature(ctx, fieldUUID)
	assert.NoError(t, err)
	assert.NotNil(t, field)
	assert.Equal(t, "Test Field", field.Name())
	assert.Equal(t, "icon", field.Icon())
	assert.Equal(t, 1, field.Position())
	assert.True(t, field.Enabled())
	assert.Len(t, field.Natures(), 2)
	assert.Equal(t, "Nature 1", field.Natures()[0].Name())
	assert.Equal(t, "Nature 2", field.Natures()[1].Name())
}

func TestFieldNatureRepository_GetFieldNature_NotFound(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT uuid, name, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnError(sql.ErrNoRows)

	_, err := repo.GetFieldNature(ctx, fieldUUID)
	assert.Error(t, err)
}

func TestFieldNatureRepository_GetFieldNature_InvalidUUID(t *testing.T) {
	db, _, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	invalidUUID := "not-a-uuid"

	_, err := repo.GetFieldNature(ctx, invalidUUID)
	assert.Error(t, err)
}

func TestFieldNatureRepository_GetFieldNature_NaturesError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &FieldNatureRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := core.NewID().Value()
	areaUUID := core.NewID().Value()
	now := time.Now()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT uuid, name, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "position", "enabled", "area_uuid", "created_at", "updated_at"}).
			AddRow(fieldUUID, "Test Field", "icon", 1, true, areaUUID, now, now))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT n.uuid, n.name, n.icon, n.enabled FROM nature n JOIN field_nature fn ON n.uuid = fn.nature_uuid WHERE fn.field_uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnError(sql.ErrConnDone)

	_, err := repo.GetFieldNature(ctx, fieldUUID)
	assert.Error(t, err)
}
