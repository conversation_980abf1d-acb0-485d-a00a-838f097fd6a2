package repository

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *FieldNatureRepository) GetFieldNature(ctx context.Context, id string) (*entity.Field, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetFieldNature"), vlog.F("action", "get field nature"))

	query := "SELECT uuid, name, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE uuid = $1"
	logger.Debug("executing", vlog.F("query", query), vlog.F("args", id))

	var row fieldRow
	err := r.db.GetContext(ctx, &row, query, id)
	if err != nil {
		logger.Error("failed to get field nature row: " + err.<PERSON>rror())
		return nil, fmt.Errorf("failed to get field nature by id: %w", err)
	}

	natures, err := r.GetNaturesByFieldUUID(ctx, row.UUID)
	if err != nil {
		logger.Error("failed to fetch natures for field", vlog.F("error", err), vlog.F("field_id", row.UUID))
		return nil, err
	}
	// Convert []*entity.Nature to []entity.Nature
	natureVals := make([]entity.Nature, len(natures))
	for i, n := range natures {
		natureVals[i] = *n
	}

	input := &entity.NewFieldInput{
		UUID:      row.UUID,
		Name:      derefString(row.Name),
		Icon:      derefString(row.Icon),
		Position:  derefInt(row.Position),
		Enabled:   row.Enabled,
		AreaUUID:  &row.AreaUUID,
		CreatedAt: &row.CreatedAt,
		UpdatedAt: &row.UpdatedAt,
		Natures:   natureVals,
	}

	field, err := entity.NewField(input)
	if err != nil {
		logger.Error("failed to construct entity from DB", vlog.F("error", err), vlog.F("db_id", row.UUID))
		return nil, err
	}

	return field, nil
}
