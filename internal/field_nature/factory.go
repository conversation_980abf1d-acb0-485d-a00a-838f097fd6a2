package field_nature

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	db     *sqlx.DB
	logger vlog.Logger
}

func NewFactory(db *sqlx.DB, logger vlog.Logger) *Factory {
	return &Factory{db: db, logger: logger}
}

func (f *Factory) getRepoOrPanic() *repository.FieldNatureRepository {
	if f == nil || f.db == nil || f.logger == nil {
		panic("Factory, db, or logger is nil")
	}
	return repository.NewFieldNatureRepository(f.db, f.logger)
}

func (f *Factory) ListFieldNaturesHandler() *transport.ListFieldNaturesHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewListFieldNaturesUsecase(repo)
	return transport.NewListFieldNaturesHandler(uc)
}

func (f *Factory) GetFieldNatureHandler() *transport.GetFieldNatureHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewGetFieldNatureUsecase(repo)
	h := transport.NewGetFieldNatureHandler(uc)
	return h
}
