package entity

import (
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

type Field struct {
	uuid      core.Identifier
	name      string
	icon      string
	position  int
	enabled   bool
	createdAt *core.Timestamp
	updatedAt *core.Timestamp
	areaUUID  *core.Identifier
	natures   []Nature
}

type NewFieldInput struct {
	UUID      string
	Name      string
	Icon      string
	Position  int
	Enabled   bool
	CreatedAt *time.Time
	UpdatedAt *time.Time
	AreaUUID  *string
	Natures   []Nature
}

func NewField(input *NewFieldInput) (*Field, error) {
	if input == nil {
		panic("NewFieldInput cannot be nil")
	}
	id, err := core.NewIDFromString(input.UUID)
	if err != nil {
		return nil, err
	}
	if input.Name == "" {
		return nil, core.NewBusinessError("field name is required")
	}
	if input.Position < 0 {
		return nil, core.NewBusinessError("field position cannot be negative")
	}
	if input.AreaUUID != nil && *input.AreaUUID == "" {
		return nil, core.NewBusinessError("area UUID cannot be an empty string")
	}
	areaId, err := core.NewIDFromString(*input.AreaUUID)
	if err != nil {
		return nil, err
	}
	if input.CreatedAt == nil && input.UpdatedAt == nil {
		return nil, core.NewBusinessError("at least one timestamp is required")
	}

	if input.Natures != nil {
		for i, n := range input.Natures {
			if n.UUID() == "" {
				return nil, fmt.Errorf("Nature UUID is required for nature[%d]", i)
			}
			if n.Name() == "" {
				return nil, fmt.Errorf("Nature name is required for nature[%d]", i)
			}
			if n.Icon() == "" {
				return nil, fmt.Errorf("Nature icon is required for nature[%d]", i)
			}
		}
	}

	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdTimestamp, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid created timestamp: %v", err)
	}

	updatedTimestamp, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid updated timestamp: %v", err)
	}

	return &Field{
		uuid:      *id,
		name:      input.Name,
		icon:      input.Icon,
		position:  input.Position,
		enabled:   input.Enabled,
		areaUUID:  areaId,
		natures:   input.Natures,
		createdAt: createdTimestamp,
		updatedAt: updatedTimestamp,
	}, nil
}

// Getter methods for Field
func (f *Field) UUID() string               { return f.uuid.Value() }
func (f *Field) Name() string               { return f.name }
func (f *Field) Icon() string               { return f.icon }
func (f *Field) Position() int              { return f.position }
func (f *Field) Enabled() bool              { return f.enabled }
func (f *Field) AreaUUID() *core.Identifier { return f.areaUUID }
func (f *Field) Natures() []Nature          { return f.natures }
func (f *Field) CreatedAt() time.Time       { return f.createdAt.Value() }
func (f *Field) UpdatedAt() time.Time       { return f.updatedAt.Value() }

// Nature entity
type Nature struct {
	uuid     core.Identifier
	name     string
	icon     string
	enabled  bool
}

type NewNatureInput struct {
	UUID     string
	Name     string
	Icon     string
	Enabled  bool
}

func NewNature(input *NewNatureInput) (*Nature, error) {
	if input == nil {
		panic("NewNatureInput cannot be nil")
	}
	id, err := core.NewIDFromString(input.UUID)
	if err != nil {
		return nil, err
	}
	if input.Name == "" {
		return nil, core.NewBusinessError("nature name is required")
	}
	if input.Icon == "" {
		return nil, core.NewBusinessError("nature icon is required")
	}
	return &Nature{
		uuid:     *id,
		name:     input.Name,
		icon:     input.Icon,
		enabled:  input.Enabled,
	}, nil
}

// Getter methods for Nature
func (n *Nature) UUID() string  { return n.uuid.Value() }
func (n *Nature) Name() string  { return n.name }
func (n *Nature) Icon() string  { return n.icon }
func (n *Nature) Enabled() bool { return n.enabled }
