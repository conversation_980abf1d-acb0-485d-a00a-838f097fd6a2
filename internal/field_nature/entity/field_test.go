package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestField_Creation(t *testing.T) {
	t.Run("valid field creation", func(t *testing.T) {
		id := core.NewID()
		areaID := core.NewID()
		created := core.NewTimestamp().Value()
		updated := core.NewTimestamp().Value()
		input := &NewFieldInput{
			UUID:      id.Value(),
			Name:      "Psychiatry",
			Icon:      "PSY",
			Position:  1,
			Enabled:   true,
			CreatedAt: &created,
			UpdatedAt: &updated,
			AreaUUID:  strPtr(areaID.Value()),
			Natures:   nil,
		}
		field, err := NewField(input)
		assert.NoError(t, err)
		assert.Equal(t, id.Value(), field.UUID())
		assert.Equal(t, "Psychiatry", field.Name())
		assert.Equal(t, "PSY", field.Icon())
		assert.Equal(t, 1, field.Position())
		assert.True(t, field.Enabled())
		assert.Equal(t, areaID.Value(), field.AreaUUID().Value())
	})

	t.Run("field with disabled status", func(t *testing.T) {
		id := core.NewID()
		areaID := core.NewID()
		created := core.NewTimestamp().Value()
		updated := core.NewTimestamp().Value()
		input := &NewFieldInput{
			UUID:      id.Value(),
			Name:      "Prosthodontics",
			Icon:      "PRO",
			Position:  1,
			Enabled:   false,
			CreatedAt: &created,
			UpdatedAt: &updated,
			AreaUUID:  strPtr(areaID.Value()),
			Natures:   nil,
		}
		field, err := NewField(input)
		assert.NoError(t, err)
		assert.Equal(t, id.Value(), field.UUID())
		assert.Equal(t, "Prosthodontics", field.Name())
		assert.Equal(t, "PRO", field.Icon())
		assert.Equal(t, 1, field.Position())
		assert.False(t, field.Enabled())
		assert.Equal(t, areaID.Value(), field.AreaUUID().Value())
	})
}

func TestField_ZeroValues(t *testing.T) {
	input := &NewFieldInput{
		UUID:      "",
		Name:      "",
		Icon:      "",
		Position:  0,
		Enabled:   false,
		CreatedAt: nil,
		UpdatedAt: nil,
		AreaUUID:  nil,
		Natures:   nil,
	}
	_, err := NewField(input)
	assert.Error(t, err)
}

func TestField_WithNatures(t *testing.T) {
	fieldID := core.NewID()
	areaID := core.NewID()
	created := core.NewTimestamp().Value()
	updated := core.NewTimestamp().Value()

	natureInput1 := &NewNatureInput{
		UUID:    core.NewID().Value(),
		Name:    "Smile Consultation",
		Icon:    "SmileConsultation",
		Enabled: true,
	}
	natureInput2 := &NewNatureInput{
		UUID:    core.NewID().Value(),
		Name:    "Veneers Consultation",
		Icon:    "VeneersConsultation",
		Enabled: false,
	}
	nature1, err := NewNature(natureInput1)
	assert.NoError(t, err)
	nature2, err := NewNature(natureInput2)
	assert.NoError(t, err)

	input := &NewFieldInput{
		UUID:      fieldID.Value(),
		Name:      "Cardiology",
		Icon:      "CARD",
		Position:  2,
		Enabled:   true,
		CreatedAt: &created,
		UpdatedAt: &updated,
		AreaUUID:  strPtr(areaID.Value()),
		Natures:   []Nature{*nature1, *nature2},
	}
	field, err := NewField(input)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(field.Natures()))
	assert.Equal(t, "Smile Consultation", field.Natures()[0].Name())
	assert.Equal(t, "Veneers Consultation", field.Natures()[1].Name())
	assert.True(t, field.Natures()[0].Enabled())
	assert.False(t, field.Natures()[1].Enabled())
}

func TestNature_Creation(t *testing.T) {
	id := core.NewID()
	input := &NewNatureInput{
		UUID:    id.Value(),
		Name:    "Test Nature",
		Icon:    "TestIcon",
		Enabled: true,
	}
	nature, err := NewNature(input)
	assert.NoError(t, err)
	assert.Equal(t, id.Value(), nature.UUID())
	assert.Equal(t, "Test Nature", nature.Name())
	assert.Equal(t, "TestIcon", nature.Icon())
	assert.True(t, nature.Enabled())
}

func strPtr(s string) *string { return &s }
