package transport

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
)

type GetFieldNatureUsecase interface {
	Execute(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error)
}

type GetFieldNatureHandler struct {
	usecase GetFieldNatureUsecase
}

type NatureModel struct {
	UUID    string `json:"uuid"`
	Name    string `json:"name"`
	Icon    string `json:"icon"`
	Enabled bool   `json:"enabled"`
}

type GetFieldNatureModel struct {
	UUID     string        `json:"uuid"`
	Name     string        `json:"name"`
	Icon     string        `json:"icon"`
	Position int           `json:"position"`
	Enabled  bool          `json:"enabled"`
	AreaUUID string        `json:"area_uuid"`
	Natures  []NatureModel `json:"natures"`
}

type GetFieldNatureResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func NewGetFieldNatureHandler(uc GetFieldNatureUsecase) *GetFieldNatureHandler {
	return &GetFieldNatureHandler{usecase: uc}
}

func (h *GetFieldNatureHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		id := c.Param("id")

		if id == "" {
			return c.JSON(http.StatusBadRequest, GetFieldNatureResponse{
				Status:  false,
				Message: "Invalid field ID format",
				Data:    []interface{}{},
			})
		}

		output, err := h.usecase.Execute(c.Request().Context(), id)
		if err != nil {
			if err.Error() == "sql: no rows in result set" ||
				err.Error() == "Field not found" ||
				strings.Contains(err.Error(), "Field not found") ||
				strings.Contains(err.Error(), "no rows in result set") ||
				strings.Contains(strings.ToLower(err.Error()), "not found") {
				return c.JSON(http.StatusNotFound, GetFieldNatureResponse{
					Status:  false,
					Message: "Field with the specified ID does not exist",
					Data:    []interface{}{},
				})
			}
			return c.JSON(http.StatusInternalServerError, GetFieldNatureResponse{
				Status:  false,
				Message: "Internal server error",
				Data:    []interface{}{},
			})
		}

		var natures []NatureModel
		for _, nature := range output.Data.Natures {
			natures = append(natures, NatureModel{
				UUID:    nature.UUID,
				Name:    nature.Name,
				Icon:    nature.Icon,
				Enabled: nature.Enabled,
			})
		}

		response := GetFieldNatureResponse{
			Status:  true,
			Message: "Field Nature retrieved successfully.",
			Data: &GetFieldNatureModel{
				UUID:     output.Data.UUID,
				Name:     output.Data.Name,
				Icon:     output.Data.Icon,
				Position: output.Data.Position,
				Enabled:  output.Data.Enabled,
				AreaUUID: output.Data.AreaUUID,
				Natures:  natures,
			},
		}
		return c.JSON(http.StatusOK, response)
	}
}
