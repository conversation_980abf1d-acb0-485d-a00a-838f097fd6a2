package transport

import (
	"context"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
)

type ListFieldNaturesUsecase interface {
	Execute(ctx context.Context, params repository.ListFieldNaturesFilter) (*usecase.ListFieldNaturesOutput, int64, error)
}

type ListFieldNaturesHandler struct {
	usecase ListFieldNaturesUsecase
}

type NaturesModel struct {
	UUID    string `json:"uuid"`
	Name    string `json:"name"`
	Icon    string `json:"icon"`
	Enabled bool   `json:"enabled"`
}

type ListFieldNaturesModel struct {
	UUID     string         `json:"uuid"`
	Name     string         `json:"name"`
	Icon     string         `json:"icon"`
	Position int            `json:"position"`
	Enabled  bool           `json:"enabled"`
	AreaUUID string         `json:"area_uuid"`
	Natures  []NaturesModel `json:"natures"`
}

type ListFieldNaturesResponse struct {
	Status     bool                   `json:"status"`
	Message    string                 `json:"message"`
	Data       interface{}            `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

func NewListFieldNaturesHandler(uc ListFieldNaturesUsecase) *ListFieldNaturesHandler {
	return &ListFieldNaturesHandler{usecase: uc}
}

func (h *ListFieldNaturesHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		page, err := strconv.Atoi(c.QueryParam("page"))
		if err != nil || page < 0 {
			page = 0
		}
		size, err := strconv.Atoi(c.QueryParam("size"))
		if err != nil || size <= 0 {
			size = 10
		}
		if size > 100 {
			size = 100
		}
		orderBy := c.QueryParam("orderBy")
		direction := c.QueryParam("direction")
		name := c.QueryParam("name")
		areaUUID := c.QueryParam("area_uuid")
		enabledStr := c.QueryParam("enabled")
		var enabled *bool
		if enabledStr != "" {
			val, err := strconv.ParseBool(enabledStr)
			if err != nil {
				resp := ListFieldNaturesResponse{
					Status:     false,
					Message:    "Invalid appointment type ID format",
					Data:       []interface{}{},
					Pagination: nil,
				}
				return c.JSON(http.StatusBadRequest, resp)
			}
			enabled = &val
		}

		params := repository.ListFieldNaturesFilter{
			Page:      page,
			Size:      size,
			OrderBy:   orderBy,
			Direction: direction,
			Name:      &name,
			AreaUUID:  &areaUUID,
			Enabled:   enabled,
		}

		output, total, err := h.usecase.Execute(c.Request().Context(), params)
		if err != nil {
			resp := ListFieldNaturesResponse{
				Status:     false,
				Message:    "Invalid appointment type ID format",
				Data:       []interface{}{},
				Pagination: nil,
			}
			return c.JSON(http.StatusBadRequest, resp)
		}

		pagination := map[string]interface{}{
			"current_page": page,
			"page_size":    size,
			"total":        total,
			"total_pages":  (total + int64(size) - 1) / int64(size),
		}

		var data []*ListFieldNaturesModel
		if output != nil && output.Data != nil {
			for _, item := range output.Data {
				var natures []NaturesModel
				for _, n := range item.Natures {
					natures = append(natures, NaturesModel{
						UUID:    n.UUID,
						Name:    n.Name,
						Icon:    n.Icon,
						Enabled: n.Enabled,
					})
				}
				data = append(data, &ListFieldNaturesModel{
					UUID:     item.UUID,
					Name:     item.Name,
					Icon:     item.Icon,
					Position: item.Position,
					Enabled:  item.Enabled,
					AreaUUID: item.AreaUUID,
					Natures:  natures,
				})
			}
		}
		if data == nil {
			data = []*ListFieldNaturesModel{}
		}

		resp := ListFieldNaturesResponse{
			Status:     true,
			Message:    "Field Natures retrieved successfully.",
			Data:       data,
			Pagination: pagination,
		}
		return c.JSON(http.StatusOK, resp)
	}
}
