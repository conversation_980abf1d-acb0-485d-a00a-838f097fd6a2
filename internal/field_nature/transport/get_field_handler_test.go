package transport

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"context"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
)

type MockFieldUsecase struct {
	GetFieldFunc func(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error)
}

func (m *MockFieldUsecase) Execute(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error) {
	if m.GetFieldFunc != nil {
		return m.GetFieldFunc(ctx, id)
	}
	return &usecase.GetFieldNatureOutput{
		Data: &usecase.GetFieldNatureOutputModel{
			UUID:     "123e4567-e89b-12d3-a456-************",
			Name:     "Test Field",
			Icon:     "icon1",
			Position: 1,
			Enabled:  true,
			AreaUUID: "area1",
			Natures: []usecase.NaturesModel{
				{UUID: "nature1", Name: "Nature 1", Icon: "iconN1", Enabled: true},
			},
		},
	}, nil
}

func TestFieldHandler_GetField(t *testing.T) {
	e := echo.New()

	t.Run("successful get field", func(t *testing.T) {
		mockUsecase := &MockFieldUsecase{}
		h := &GetFieldNatureHandler{usecase: mockUsecase}

		fieldID := "123e4567-e89b-12d3-a456-************"
		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(fieldID)

		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetFieldNatureResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, "Field Nature retrieved successfully.", response.Message)
		model, ok := response.Data.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, fieldID, model["uuid"])
	})

	t.Run("missing id param", func(t *testing.T) {
		mockUsecase := &MockFieldUsecase{}
		h := &GetFieldNatureHandler{usecase: mockUsecase}

		req := httptest.NewRequest(http.MethodGet, "/fields/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		var response GetFieldNatureResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Invalid field ID format", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})

	t.Run("field not found", func(t *testing.T) {
		mockUsecase := &MockFieldUsecase{
			GetFieldFunc: func(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error) {
				return nil, errors.New("Field not found")
			},
		}
		h := &GetFieldNatureHandler{usecase: mockUsecase}

		fieldID := "123e4567-e89b-12d3-a456-************"
		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(fieldID)

		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
		var response GetFieldNatureResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Field with the specified ID does not exist", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})

	t.Run("field not found - sql no rows", func(t *testing.T) {
		mockUsecase := &MockFieldUsecase{
			GetFieldFunc: func(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error) {
				return nil, errors.New("sql: no rows in result set")
			},
		}
		h := &GetFieldNatureHandler{usecase: mockUsecase}

		fieldID := "123e4567-e89b-12d3-a456-************"
		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(fieldID)

		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
		var response GetFieldNatureResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Field with the specified ID does not exist", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})

	t.Run("field not found - substring", func(t *testing.T) {
		mockUsecase := &MockFieldUsecase{
			GetFieldFunc: func(ctx context.Context, id string) (*usecase.GetFieldNatureOutput, error) {
				return nil, errors.New("no rows in result set")
			},
		}
		h := &GetFieldNatureHandler{usecase: mockUsecase}

		fieldID := "123e4567-e89b-12d3-a456-************"
		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(fieldID)

		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
		var response GetFieldNatureResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Field with the specified ID does not exist", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})
}
