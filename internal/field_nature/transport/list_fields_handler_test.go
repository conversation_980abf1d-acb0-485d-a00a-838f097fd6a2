package transport

import (
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
)

type mockListFieldNaturesUsecase struct{}

func (m *mockListFieldNaturesUsecase) Execute(ctx context.Context, params repository.ListFieldNaturesFilter) (*usecase.ListFieldNaturesOutput, int64, error) {
	return &usecase.ListFieldNaturesOutput{
		Data: []*usecase.ListFieldNaturesOutputModel{
			{
				UUID:     "uuid1",
				Name:     "Field 1",
				Icon:     "icon1",
				Position: 1,
				Enabled:  true,
				AreaUUID: "area1",
				Natures: []usecase.NaturesModel{
					{UUID: "nature1", Name: "Nature 1", Icon: "iconN1", Enabled: true},
				},
			},
		},
	}, 1, nil
}

type emptyMock struct{}

func (m *emptyMock) Execute(ctx context.Context, params repository.ListFieldNaturesFilter) (*usecase.ListFieldNaturesOutput, int64, error) {
	return &usecase.ListFieldNaturesOutput{Data: []*usecase.ListFieldNaturesOutputModel{}}, 0, nil
}

func TestListFieldNaturesHandler_Handle(t *testing.T) {
	e := echo.New()

	t.Run("non-empty result", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/fields?page=0&size=1", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		h := &ListFieldNaturesHandler{usecase: &mockListFieldNaturesUsecase{}}
		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, 200, rec.Code)

		var response ListFieldNaturesResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, "Field Natures retrieved successfully.", response.Message)
		if arr, ok := response.Data.([]interface{}); ok {
			assert.NotEqual(t, 0, len(arr))
		}
	})

	t.Run("empty result", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/fields?page=0&size=1", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		h := &ListFieldNaturesHandler{usecase: &emptyMock{}}
		err := h.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, 200, rec.Code)

		var response ListFieldNaturesResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, "Field Natures retrieved successfully.", response.Message)
		if arr, ok := response.Data.([]interface{}); ok {
			assert.Equal(t, 0, len(arr))
		}
	})
}
