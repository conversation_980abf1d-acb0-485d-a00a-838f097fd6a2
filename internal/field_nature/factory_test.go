package field_nature

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestFactoryHandlers(t *testing.T) {
	t.Run("creates ListFieldNaturesHandler with all dependencies wired", func(t *testing.T) {
		db, _ := setupMockDB(t)
		factory := NewFactory(db, vlog.NewWithLevel("error"))
		handler := factory.ListFieldNaturesHandler()
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.ListFieldNaturesHandler{}, handler)
	})

	t.Run("creates GetFieldNatureHandler with all dependencies wired", func(t *testing.T) {
		db, _ := setupMockDB(t)
		factory := NewFactory(db, vlog.NewWithLevel("error"))
		handler := factory.GetFieldNatureHandler()
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.GetFieldNatureHandler{}, handler)
	})

	t.Run("multiple factory calls create independent handler instances", func(t *testing.T) {
		db, _ := setupMockDB(t)
		factory := NewFactory(db, vlog.NewWithLevel("error"))
		handler1 := factory.ListFieldNaturesHandler()
		handler2 := factory.ListFieldNaturesHandler()
		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2)
	})
}
