package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type ListFieldNaturesRepository interface {
	ListFieldNatures(ctx context.Context, params repository.ListFieldNaturesFilter) ([]*entity.Field, int64, error)
}

type ListFieldNaturesUsecase struct {
	repo ListFieldNaturesRepository
}

type NaturesModel struct {
	UUID    string
	Name    string
	Icon    string
	Enabled bool
}

type ListFieldNaturesOutputModel struct {
	UUID     string
	Name     string
	Icon     string
	Position int
	Enabled  bool
	AreaUUID string
	Natures  []NaturesModel
}

type ListFieldNaturesOutput struct {
	Data []*ListFieldNaturesOutputModel
}

func NewListFieldNaturesUsecase(repo ListFieldNaturesRepository) *ListFieldNaturesUsecase {
	return &ListFieldNaturesUsecase{
		repo: repo,
	}
}

func (u *ListFieldNaturesUsecase) Execute(ctx context.Context, params repository.ListFieldNaturesFilter) (*ListFieldNaturesOutput, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "ListFieldNaturesUsecase"))
	logger.Debug("trying to execute")

	fields, total, err := u.repo.ListFieldNatures(ctx, params)
	if err != nil {
		return nil, 0, err
	}

	var fieldModels []*ListFieldNaturesOutputModel
	for _, field := range fields {
		if field == nil {
			fieldModels = append(fieldModels, nil)
			continue
		}
		fieldModels = append(fieldModels, &ListFieldNaturesOutputModel{
			UUID:     field.UUID(),
			Name:     field.Name(),
			Icon:     field.Icon(),
			Position: field.Position(),
			Enabled:  field.Enabled(),
			AreaUUID: field.AreaUUID().Value(),
			Natures:  toNaturesModels(field.Natures()),
		})
	}

	return &ListFieldNaturesOutput{
		Data: fieldModels,
	}, total, nil
}

func toNaturesModels(natures []entity.Nature) []NaturesModel {
	var models []NaturesModel
	for _, n := range natures {
		models = append(models, NaturesModel{
			UUID:    n.UUID(),
			Name:    n.Name(),
			Icon:    n.Icon(),
			Enabled: n.Enabled(),
		})
	}
	return models
}
