package usecase

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
)

type mockGetFieldRepo struct {
	GetByIDFunc func(ctx context.Context, id string) (*entity.Field, error)
}

func (m *mockGetFieldRepo) GetByID(ctx context.Context, id string) (*entity.Field, error) {
	return m.GetByIDFunc(ctx, id)
}

func TestFieldUsecase_GetFieldUsecase(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		repo := &mockGetFieldNatureRepo{
			GetFieldNatureFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				created := time.Now()
				areaUUID := "123e4567-e89b-12d3-a456-************"
				field, err := entity.NewField(&entity.NewFieldInput{
					UUID:      "123e4567-e89b-12d3-a456-************",
					Name:      "Test Field",
					Icon:      "icon.png",
					Position:  1,
					Enabled:   true,
					CreatedAt: &created,
					UpdatedAt: nil,
					AreaUUID:  &areaUUID,
					Natures:   nil,
				})
				if err != nil {
					return nil, err
				}
				return field, nil
			},
		}
		uc := NewGetFieldNatureUsecase(repo)
		resp, err := uc.Execute(context.Background(), "some-id")
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "Test Field", resp.Data.Name)
	})

	t.Run("error", func(t *testing.T) {
		repo := &mockGetFieldNatureRepo{
			GetFieldNatureFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				return nil, errors.New("not found")
			},
		}
		uc := NewGetFieldNatureUsecase(repo)
		resp, err := uc.Execute(context.Background(), "some-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})
}

type mockGetFieldNatureRepo struct {
	GetFieldNatureFunc func(ctx context.Context, id string) (*entity.Field, error)
}

func (m *mockGetFieldNatureRepo) GetFieldNature(ctx context.Context, id string) (*entity.Field, error) {
	return m.GetFieldNatureFunc(ctx, id)
}

func TestGetFieldNatureUsecase_Execute(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		repo := &mockGetFieldNatureRepo{
			GetFieldNatureFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				created := time.Now()
				areaUUID := "123e4567-e89b-12d3-a456-************"
				field, err := entity.NewField(&entity.NewFieldInput{
					UUID:      "123e4567-e89b-12d3-a456-************",
					Name:      "Test Field",
					Icon:      "icon.png",
					Position:  1,
					Enabled:   true,
					CreatedAt: &created,
					UpdatedAt: nil,
					AreaUUID:  &areaUUID,
					Natures:   nil,
				})
				if err != nil {
					return nil, err
				}
				return field, nil
			},
		}
		uc := NewGetFieldNatureUsecase(repo)
		resp, err := uc.Execute(context.Background(), "some-id")
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "Test Field", resp.Data.Name)
	})

	t.Run("error", func(t *testing.T) {
		repo := &mockGetFieldNatureRepo{
			GetFieldNatureFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				return nil, errors.New("not found")
			},
		}
		uc := NewGetFieldNatureUsecase(repo)
		resp, err := uc.Execute(context.Background(), "some-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})
}
