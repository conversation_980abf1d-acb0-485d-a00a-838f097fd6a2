package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type GetFieldNatureRepository interface {
	GetFieldNature(ctx context.Context, id string) (*entity.Field, error)
}

type GetFieldNatureUsecase struct {
	repo GetFieldNatureRepository
}

type NatureModel struct {
	UUID    string
	Name    string
	Icon    string
	Enabled bool
}

type GetFieldNatureOutputModel struct {
	UUID     string
	Name     string
	Icon     string
	Position int
	Enabled  bool
	AreaUUID string
	Natures  []NaturesModel
}

type GetFieldNatureOutput struct {
	Data *GetFieldNatureOutputModel
}

func NewGetFieldNatureUsecase(repo GetFieldNatureRepository) *GetFieldNatureUsecase {
	return &GetFieldNatureUsecase{
		repo: repo,
	}
}

func (u *GetFieldNatureUsecase) Execute(ctx context.Context, id string) (*GetFieldNatureOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "GetFieldNaturesUsecase"))
	logger.Debug("trying to execute")

	field, err := u.repo.GetFieldNature(ctx, id)
	if err != nil {
		return nil, err
	}

	return &GetFieldNatureOutput{
		Data: &GetFieldNatureOutputModel{
			UUID:     field.UUID(),
			Name:     field.Name(),
			Icon:     field.Icon(),
			Position: field.Position(),
			Enabled:  field.Enabled(),
			AreaUUID: field.AreaUUID().Value(),
			Natures:  toNaturesModels(field.Natures()),
		},
	}, nil
}
