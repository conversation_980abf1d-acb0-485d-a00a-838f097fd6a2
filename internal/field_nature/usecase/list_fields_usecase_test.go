package usecase

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
)

type mockListFieldsRepo struct {
	ListFieldNaturesFunc func(ctx context.Context, params repository.ListFieldNaturesFilter) ([]*entity.Field, int64, error)
}

func (m *mockListFieldsRepo) ListFieldNatures(ctx context.Context, params repository.ListFieldNaturesFilter) ([]*entity.Field, int64, error) {
	return m.ListFieldNaturesFunc(ctx, params)
}

func TestListFieldNaturesUsecase_Execute(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		repo := &mockListFieldsRepo{
			ListFieldNaturesFunc: func(ctx context.Context, params repository.ListFieldNaturesFilter) ([]*entity.Field, int64, error) {
				created := time.Now()
				areaUUID := "123e4567-e89b-12d3-a456-************"
				field, err := entity.NewField(&entity.NewFieldInput{
					UUID:      "123e4567-e89b-12d3-a456-************",
					Name:      "Field 1",
					Icon:      "icon.png",
					Position:  1,
					Enabled:   true,
					CreatedAt: &created,
					UpdatedAt: nil,
					AreaUUID:  &areaUUID,
					Natures:   nil,
				})
				if err != nil {
					return nil, 0, err
				}
				return []*entity.Field{field}, 1, nil
			},
		}
		uc := NewListFieldNaturesUsecase(repo)
		resp, total, err := uc.Execute(context.Background(), repository.ListFieldNaturesFilter{})
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Data, 1)
		assert.Equal(t, "Field 1", resp.Data[0].Name)
		assert.Equal(t, int64(1), total)
	})

	t.Run("error", func(t *testing.T) {
		repo := &mockListFieldsRepo{
			ListFieldNaturesFunc: func(ctx context.Context, params repository.ListFieldNaturesFilter) ([]*entity.Field, int64, error) {
				return nil, 0, errors.New("db error")
			},
		}
		uc := NewListFieldNaturesUsecase(repo)
		resp, total, err := uc.Execute(context.Background(), repository.ListFieldNaturesFilter{})
		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Equal(t, int64(0), total)
	})
}
