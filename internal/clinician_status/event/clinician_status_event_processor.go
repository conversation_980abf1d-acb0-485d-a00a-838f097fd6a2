package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertClinicianStatusUsecase defines the interface for clinician status upsert operations
type UpsertClinicianStatusUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertClinicianStatusInput) (*usecase.UpsertClinicianStatusOutput, error)
}

// ClinicianStatusUpsertEventProcessor processes clinician status upsert events
type ClinicianStatusUpsertEventProcessor struct {
	upsertUsecase UpsertClinicianStatusUsecase
}

// NewClinicianStatusUpsertEventProcessor creates a new clinician status upsert event processor
func NewClinicianStatusUpsertEventProcessor(upsertUsecase UpsertClinicianStatusUsecase) *ClinicianStatusUpsertEventProcessor {
	return &ClinicianStatusUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// IsBusinessError determines if an error is a business logic error that should go to DLQ
func (p *ClinicianStatusUpsertEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"clinician status name",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"clinician_status_name_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	// Check for core business errors
	var businessErr *core.BusinessError
	if errors.As(err, &businessErr) {
		return true
	}

	return false
}

// ProcessMessage processes a clinician status event message
func (p *ClinicianStatusUpsertEventProcessor) ProcessMessage(ctx context.Context, message *ClinicianStatusEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "ClinicianStatusUpsertEventProcessor"))
	logger.Debug("handling clinician status event message")

	// Validate message data
	if message.Name == "" {
		logger.Error("validation failed", vlog.F("error", "clinician status name is required"))
		return core.NewBusinessError("clinician status name is required in message data")
	}

	// Validate ID format if provided
	if message.ID != nil && *message.ID != "" {
		if _, err := core.NewIDFromString(*message.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertClinicianStatusInput{
		Name:      message.Name,
		Enabled:   message.Enabled,
		UpdatedAt: message.UpdatedAt,
	}

	if message.ID != nil {
		upsertInput.ID = message.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("clinician_status", message.Name),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			// Return a business error that SQS will recognize as non-retryable
			return core.NewBusinessError("Business error processing clinician status '%s': %v", message.Name, err)
		}

		// This is a transient error - should be retried
		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("clinician_status", message.Name),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing clinician status upsert: %w", err)
	}

	logger.Info("successfully processed clinician status upsert",
		vlog.F("name", message.Name))
	return nil
}
