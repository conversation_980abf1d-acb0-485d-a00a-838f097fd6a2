package event

import (
	"time"
)

// ClinicianStatusEventMessage represents the event message structure for clinician status operations
type ClinicianStatusEventMessage struct {
	ID        *string    `json:"id,omitempty"`
	Name      string     `json:"name"`
	Enabled   bool       `json:"enabled"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
}
