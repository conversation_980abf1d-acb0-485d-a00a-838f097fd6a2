package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var upsertClinicianStatusUsecaseName = "UpsertClinicianStatusUsecase"

type ClinicianStatusRepository interface {
	UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error
}

type UpsertClinicianStatusInput struct {
	ID        *string
	Name      string
	Enabled   bool
	UpdatedAt *time.Time
}

type UpsertClinicianStatusOutput struct {
	ID      string
	Name    string
	Enabled bool
}

type UpsertClinicianStatusUsecase struct {
	repo              ClinicianStatusRepository
	validationService service.ClinicianStatusValidationService
}

func NewUpsertClinicianStatusUsecase(repo ClinicianStatusRepository, validationService service.ClinicianStatusValidationService) *UpsertClinicianStatusUsecase {
	return &UpsertClinicianStatusUsecase{
		repo:              repo,
		validationService: validationService,
	}
}

func (uc *UpsertClinicianStatusUsecase) Execute(ctx context.Context, input *UpsertClinicianStatusInput) (*UpsertClinicianStatusOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", upsertClinicianStatusUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate input
	if err := uc.validateInput(input); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return nil, err
	}

	// Validate using service layer business rules
	serviceInput := &service.UpsertValidationInput{
		ID:      input.ID,
		Name:    input.Name,
		Enabled: input.Enabled,
	}
	if err := uc.validationService.ValidateUpsertInput(ctx, serviceInput); err != nil {
		logger.Error("service validation failed", vlog.F("error", err))
		return nil, err
	}

	// Determine entity ID
	entityID := core.NewID().Value()
	if input.ID != nil && *input.ID != "" {
		entityID = *input.ID
	}

	// Create clinician status entity
	clinicianStatus, err := entity.NewClinicianStatus(&entity.NewClinicianStatusInput{
		ID:        entityID,
		Name:      input.Name,
		Enabled:   input.Enabled,
		CreatedAt: nil,
		UpdatedAt: input.UpdatedAt,
	})
	if err != nil {
		logger.Error("failed to create clinician status entity", vlog.F("error", err))
		return nil, core.NewBusinessError("failed to create clinician status entity: %v", err)
	}

	if err := uc.validationService.ValidateBusinessRules(ctx, clinicianStatus); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return nil, err
	}

	// Upsert clinician status entity
	if err := uc.repo.UpsertClinicianStatus(ctx, clinicianStatus); err != nil {
		// Check if it's a business error
		var businessErr *core.BusinessError
		if errors.As(err, &businessErr) {
			logger.Error("clinician status constraint violation", vlog.F("error", err), vlog.F("clinician_status_name", clinicianStatus.Name()))
			return nil, err
		}

		logger.Error("failed to upsert clinician status", vlog.F("error", err))
		return nil, fmt.Errorf("database error occurred while upserting clinician status: %w", err)
	}

	out := &UpsertClinicianStatusOutput{
		ID:      clinicianStatus.ID(),
		Name:    clinicianStatus.Name(),
		Enabled: clinicianStatus.Enabled(),
	}

	logger.Debug("execution finished", vlog.F("output", out))
	return out, nil
}

// validateInput validates the input
func (uc *UpsertClinicianStatusUsecase) validateInput(input *UpsertClinicianStatusInput) error {
	if input == nil {
		return core.NewBusinessError("input is required")
	}

	// Use core validation patterns
	if input.Name == "" {
		return core.NewBusinessError("name is required")
	}

	// Validate UUID format if provided
	if input.ID != nil && *input.ID != "" {
		if _, err := core.NewIDFromString(*input.ID); err != nil {
			return core.NewBusinessError("invalid UUID format: %v", err)
		}
	}

	return nil
}
