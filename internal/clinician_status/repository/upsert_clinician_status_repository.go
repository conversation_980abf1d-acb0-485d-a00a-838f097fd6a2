package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// ClinicianStatusUpsertRepository defines the interface for clinician status upsert operations
type ClinicianStatusUpsertRepository interface {
	UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error
}

// clinicianStatusUpsertRepository implements ClinicianStatusUpsertRepository
type clinicianStatusUpsertRepository struct {
	db *sqlx.DB
}

// NewClinicianStatusUpsertRepository creates a new clinician status upsert repository
func NewClinicianStatusUpsertRepository(db *sqlx.DB) *clinicianStatusUpsertRepository {
	return &clinicianStatusUpsertRepository{
		db: db,
	}
}

// UpsertClinicianStatus upserts a clinician status entity into the database
func (r *clinicianStatusUpsertRepository) UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "ClinicianStatusRepository"), vlog.F("method", "UpsertClinicianStatus"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.clinician_status WHERE uuid = $1`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("uuid", clinicianStatus.ID()))
	err := r.db.QueryRowContext(ctx, checkQuery, clinicianStatus.ID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing clinician status", vlog.F("error", err))
		return fmt.Errorf("failed to check existing clinician status: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", clinicianStatus.UpdatedAt()),
			vlog.F("incoming_is_newer", clinicianStatus.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && clinicianStatus.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("clinician status event discarded - older than existing record",
			vlog.F("id", clinicianStatus.ID()),
			vlog.F("name", clinicianStatus.Name()),
			vlog.F("event_updated_at", clinicianStatus.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.clinician_status (uuid, clinician_status, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (uuid) DO UPDATE SET
			clinician_status = EXCLUDED.clinician_status,
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.clinician_status.updated_at
	`

	args := []interface{}{
		clinicianStatus.ID(),
		clinicianStatus.Name(),
		clinicianStatus.Enabled(),
		clinicianStatus.CreatedAt(),
		clinicianStatus.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)
	if err != nil {
		// Check for name constraint violation
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr), vlog.F("clinician_status", clinicianStatus.Name()))
			return parsedErr
		}

		logger.Error("failed to upsert clinician status", vlog.F("error", err))
		return fmt.Errorf("failed to upsert clinician status: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("clinician status updated successfully",
			vlog.F("id", clinicianStatus.ID()),
			vlog.F("name", clinicianStatus.Name()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("clinician status inserted successfully",
			vlog.F("id", clinicianStatus.ID()),
			vlog.F("name", clinicianStatus.Name()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
