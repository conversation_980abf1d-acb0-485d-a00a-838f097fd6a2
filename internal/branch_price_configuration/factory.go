package BranchPriceConfiguration

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	db     *sqlx.DB
	logger vlog.Logger
}

func NewFactory(db *sqlx.DB, logger vlog.Logger) *Factory {
	return &Factory{
		db:     db,
		logger: logger,
	}
}

func (f *Factory) CreateBranchPriceConfigurationHandler() *transport.CreateBranchPriceConfigurationHandler {
	repo := repository.NewBranchPriceConfigurationRepo(f.db)
	uc := usecase.NewBranchPriceConfigurationUsecase(repo)
	h := transport.NewCreateBranchPriceConfigurationHandler(uc)
	return h
}

func (f *Factory) GetBranchPriceConfigurationHandler() *transport.GetBranchPriceConfigurationHandler {
	repo := repository.NewBranchPriceConfigurationRepo(f.db)
	uc := usecase.NewGetBranchPriceConfigurationUsecase(repo)
	h := transport.NewGetBranchPriceConfigurationHandler(uc)
	return h
}
