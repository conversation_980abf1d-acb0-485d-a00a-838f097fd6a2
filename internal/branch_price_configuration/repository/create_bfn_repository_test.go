package repository

import (
	"context"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
)

func newTestBranchPriceConfiguration() *entity.BranchPriceConfiguration {
	id := core.NewID()
	branchID := core.NewID()
	fieldID := core.NewID()
	natureID := core.NewID()
	apptType := "in_clinic"
	return &entity.BranchPriceConfiguration{
		ID:              *id,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "pro",
		Prices:          100,
		Duration:        30,
		AppointmentType: &apptType,
		PriceListType:   "appointment",
		PriceType:       "fixed",
	}
}

func TestBranchPriceConfigurationRepository_BatchCreate_Success(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)
	bfn1 := newTestBranchPriceConfiguration()
	bfn2 := newTestBranchPriceConfiguration()
	bfns := []*entity.BranchPriceConfiguration{bfn1, bfn2}

	mock.ExpectBegin()
	for _, bfn := range bfns {
		mock.ExpectQuery("INSERT INTO branch_price_configuration").
			WithArgs(
				bfn.BranchID.Value(),
				bfn.FieldID.Value(),
				bfn.NatureID.Value(),
				bfn.ClinicianStatus,
				bfn.Prices,
				bfn.Duration,
				bfn.AppointmentType,
				bfn.PriceListType,
				bfn.PriceType,
			).
			WillReturnRows(sqlmock.NewRows([]string{"id", "created_date", "updated_date"}).
				AddRow(bfn.ID.Value(), "2025-09-08T10:00:00Z", "2025-09-08T10:00:00Z"))
	}
	mock.ExpectCommit()

	result, err := repo.BatchCreate(context.Background(), bfns)
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, bfn1.ID, result[0].ID)
	assert.Equal(t, bfn2.ID, result[1].ID)
}

func TestBranchPriceConfigurationRepository_BatchCreate_ScanError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)
	bfn := newTestBranchPriceConfiguration()
	bfns := []*entity.BranchPriceConfiguration{bfn}

	mock.ExpectBegin()
	mock.ExpectQuery("INSERT INTO branch_price_configuration").
		WithArgs(
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_date"})) // missing updated_date

	mock.ExpectRollback()
	_, err := repo.BatchCreate(context.Background(), bfns)
	assert.Error(t, err)
}

func TestBranchPriceConfigurationRepository_BatchCreate_DBError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)
	bfn := newTestBranchPriceConfiguration()
	bfns := []*entity.BranchPriceConfiguration{bfn}

	mock.ExpectBegin()
	mock.ExpectQuery("INSERT INTO branch_price_configuration").
		WithArgs(
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnError(errors.New("db error"))

	mock.ExpectRollback()
	_, err := repo.BatchCreate(context.Background(), bfns)
	assert.Error(t, err)
}

func TestBranchPriceConfigurationRepository_BatchCreate_InvalidID(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)
	bfn := newTestBranchPriceConfiguration()
	bfns := []*entity.BranchPriceConfiguration{bfn}

	mock.ExpectBegin()
	mock.ExpectQuery("INSERT INTO branch_price_configuration").
		WithArgs(
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_date", "updated_date"}).
			AddRow("invalid-uuid", "2025-09-08T10:00:00Z", "2025-09-08T10:00:00Z"))

	mock.ExpectRollback()
	_, err := repo.BatchCreate(context.Background(), bfns)
	assert.Error(t, err)
}
