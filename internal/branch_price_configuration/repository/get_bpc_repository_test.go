package repository

import (
	"context"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
)

func TestBranchPriceConfigurationRepository_GetBranchPriceConfigurations_Success(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)

	branchID := "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
	filter := GetBranchPriceConfigurationFilter{
		BranchID: branchID,
	}

	// Mock the complex query result
	rows := sqlmock.NewRows([]string{
		"area_id", "area_label", "field_id", "field_name", "nature_id", "nature_name",
		"clinician_status", "appointment_type", "prices", "duration", "price_list_type", "price_type", "field_status_acronym",
	}).
		AddRow("4beed17b-a38a-4da1-8b26-94d2f1513001", "Dentistry", "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75", "Field Of Dentistry",
			"171977db-0846-4dac-af9f-30d6a43f3dac", "Aesthetic Consultation", "master", "in_clinic", 45, 30, "appointment", "fixed", "master").
		AddRow("4beed17b-a38a-4da1-8b26-94d2f1513001", "Dentistry", "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75", "Field Of Dentistry",
			"5fc76a15-e2a9-4442-89ee-7cc8e1134de7", "Bonding Consultation", nil, nil, nil, nil, nil, nil, "elite")

	mock.ExpectQuery(`SELECT DISTINCT`).
		WithArgs(branchID).
		WillReturnRows(rows)

	result, err := repo.GetBranchPriceConfigurations(context.Background(), filter)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Data, 2)

	// Check first row with price configuration
	firstRow := result.Data[0]
	assert.Equal(t, "4beed17b-a38a-4da1-8b26-94d2f1513001", firstRow.AreaID)
	assert.Equal(t, "Dentistry", firstRow.AreaLabel)
	assert.Equal(t, "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75", firstRow.FieldID)
	assert.Equal(t, "Field Of Dentistry", firstRow.FieldName)
	assert.Equal(t, "171977db-0846-4dac-af9f-30d6a43f3dac", firstRow.NatureID)
	assert.Equal(t, "Aesthetic Consultation", firstRow.NatureName)
	assert.NotNil(t, firstRow.ClinicianStatus)
	assert.Equal(t, "master", *firstRow.ClinicianStatus)
	assert.NotNil(t, firstRow.AppointmentType)
	assert.Equal(t, "in_clinic", *firstRow.AppointmentType)
	assert.NotNil(t, firstRow.Prices)
	assert.Equal(t, 45, *firstRow.Prices)
	assert.NotNil(t, firstRow.Duration)
	assert.Equal(t, 30, *firstRow.Duration)
	assert.NotNil(t, firstRow.PriceListType)
	assert.Equal(t, "appointment", *firstRow.PriceListType)
	assert.NotNil(t, firstRow.PriceType)
	assert.Equal(t, "fixed", *firstRow.PriceType)
	assert.Equal(t, "master", firstRow.FieldStatusAcronym)

	// Check second row without price configuration
	secondRow := result.Data[1]
	assert.Equal(t, "4beed17b-a38a-4da1-8b26-94d2f1513001", secondRow.AreaID)
	assert.Equal(t, "Dentistry", secondRow.AreaLabel)
	assert.Equal(t, "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75", secondRow.FieldID)
	assert.Equal(t, "Field Of Dentistry", secondRow.FieldName)
	assert.Equal(t, "5fc76a15-e2a9-4442-89ee-7cc8e1134de7", secondRow.NatureID)
	assert.Equal(t, "Bonding Consultation", secondRow.NatureName)
	assert.Nil(t, secondRow.ClinicianStatus)
	assert.Nil(t, secondRow.AppointmentType)
	assert.Nil(t, secondRow.Prices)
	assert.Nil(t, secondRow.Duration)
	assert.Nil(t, secondRow.PriceListType)
	assert.Nil(t, secondRow.PriceType)
	assert.Equal(t, "elite", secondRow.FieldStatusAcronym)
}

func TestBranchPriceConfigurationRepository_GetBranchPriceConfigurations_EmptyResult(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)

	branchID := "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
	filter := GetBranchPriceConfigurationFilter{
		BranchID: branchID,
	}

	// Mock empty result
	rows := sqlmock.NewRows([]string{
		"area_id", "area_label", "field_id", "field_name", "nature_id", "nature_name",
		"clinician_status", "appointment_type", "prices", "duration", "price_list_type", "price_type", "field_status_acronym",
	})

	mock.ExpectQuery(`SELECT DISTINCT`).
		WithArgs(branchID).
		WillReturnRows(rows)

	result, err := repo.GetBranchPriceConfigurations(context.Background(), filter)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Data, 0)
}

func TestBranchPriceConfigurationRepository_GetBranchPriceConfigurations_DBError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)

	branchID := "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
	filter := GetBranchPriceConfigurationFilter{
		BranchID: branchID,
	}

	mock.ExpectQuery(`SELECT DISTINCT`).
		WithArgs(branchID).
		WillReturnError(errors.New("database connection error"))

	result, err := repo.GetBranchPriceConfigurations(context.Background(), filter)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database connection error")
}

func TestBranchPriceConfigurationRepository_GetBranchPriceConfigurations_InvalidBranchID(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewBranchPriceConfigurationRepo(sqlxDB)

	branchID := "invalid-uuid"
	filter := GetBranchPriceConfigurationFilter{
		BranchID: branchID,
	}

	mock.ExpectQuery(`SELECT DISTINCT`).
		WithArgs(branchID).
		WillReturnError(errors.New("invalid input syntax for type uuid"))

	result, err := repo.GetBranchPriceConfigurations(context.Background(), filter)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "invalid input syntax for type uuid")
}
