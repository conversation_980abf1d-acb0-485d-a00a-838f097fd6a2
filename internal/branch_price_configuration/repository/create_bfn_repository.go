package repository

import (
	"context"

	"github.com/jmoiron/sqlx"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type BranchPriceConfigurationRepository struct {
	db *sqlx.DB
}

func NewBranchPriceConfigurationRepo(db *sqlx.DB) *BranchPriceConfigurationRepository {
	return &BranchPriceConfigurationRepository{
		db: db,
	}
}

func (r *BranchPriceConfigurationRepository) BatchCreate(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "Create"), vlog.F("action", "create branch price configuration"))

	tx, err := r.db.Beginx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	query := `INSERT INTO branch_price_configuration (
		branch_id, field_id, nature_id, clinician_status, prices, duration, appointment_type, price_list_type, price_type, created_date, updated_date
	) VALUES (
		$1, $2, $3, $4, $5, $6, $7, $8, $9, now(), now()
	) RETURNING id, created_date, updated_date`

	var results []*entity.BranchPriceConfiguration
	for _, bfn := range bfns {
		row := tx.QueryRowx(query,
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		)
		var idStr string
		var createdDate, updatedDate interface{}
		if err := row.Scan(&idStr, &createdDate, &updatedDate); err != nil {
			logger.Error("failed to insert branch price configuration",
				vlog.F("error", err),
				vlog.F("branch_id", bfn.BranchID.Value()),
				vlog.F("field_id", bfn.FieldID.Value()),
				vlog.F("nature_id", bfn.NatureID.Value()),
				vlog.F("clinician_status", bfn.ClinicianStatus),
				vlog.F("prices", bfn.Prices),
				vlog.F("duration", bfn.Duration),
				vlog.F("appointment_type", bfn.AppointmentType),
				vlog.F("price_list_type", bfn.PriceListType),
				vlog.F("price_type", bfn.PriceType),
			)
			return nil, err
		}
		id, idErr := core.NewIDFromString(idStr)
		if idErr != nil {
			logger.Error("invalid id returned from DB",
				vlog.F("error", idErr),
				vlog.F("idStr", idStr),
			)
			tx.Rollback()
			return nil, idErr
		}
		bfn.ID = *id
		results = append(results, bfn)
	}
	if err := tx.Commit(); err != nil {
		logger.Error("failed to create branch price configuration", vlog.F("error", err))
		return nil, err
	}
	return results, nil
}
