package repository

import (
	"context"

	"github.com/jmoiron/sqlx"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type BranchPriceConfigurationRepository struct {
	db *sqlx.DB
}

func NewBranchPriceConfigurationRepo(db *sqlx.DB) *BranchPriceConfigurationRepository {
	return &BranchPriceConfigurationRepository{
		db: db,
	}
}

func (r *BranchPriceConfigurationRepository) BatchCreate(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "Create"), vlog.F("action", "create branch price configuration"))

	tx, err := r.db.Beginx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	query := `INSERT INTO branch_price_configuration (
		branch_id, field_id, nature_id, clinician_status, prices, duration, appointment_type, price_list_type, price_type, created_date, updated_date
	) VALUES (
		$1, $2, $3, $4, $5, $6, $7, $8, $9, now(), now()
	) RETURNING id, created_date, updated_date`

	var results []*entity.BranchPriceConfiguration
	for _, bfn := range bfns {
		row := tx.QueryRowx(query,
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		)
		var idStr string
		var createdDate, updatedDate interface{}
		if err := row.Scan(&idStr, &createdDate, &updatedDate); err != nil {
			logger.Error("failed to insert branch price configuration",
				vlog.F("error", err),
				vlog.F("branch_id", bfn.BranchID.Value()),
				vlog.F("field_id", bfn.FieldID.Value()),
				vlog.F("nature_id", bfn.NatureID.Value()),
				vlog.F("clinician_status", bfn.ClinicianStatus),
				vlog.F("prices", bfn.Prices),
				vlog.F("duration", bfn.Duration),
				vlog.F("appointment_type", bfn.AppointmentType),
				vlog.F("price_list_type", bfn.PriceListType),
				vlog.F("price_type", bfn.PriceType),
			)
			return nil, err
		}
		id, idErr := core.NewIDFromString(idStr)
		if idErr != nil {
			logger.Error("invalid id returned from DB",
				vlog.F("error", idErr),
				vlog.F("idStr", idStr),
			)
			tx.Rollback()
			return nil, idErr
		}
		bfn.ID = *id
		results = append(results, bfn)
	}
	if err := tx.Commit(); err != nil {
		logger.Error("failed to create branch price configuration", vlog.F("error", err))
		return nil, err
	}
	return results, nil
}

// GetBranchPriceConfigurationFilter represents filter parameters for fetching branch price configurations
type GetBranchPriceConfigurationFilter struct {
	BranchID string
}

// GetBranchPriceConfigurationModel represents the database model for the complex query result
type GetBranchPriceConfigurationModel struct {
	AreaID             string  `db:"area_id"`
	AreaLabel          string  `db:"area_label"`
	FieldID            string  `db:"field_id"`
	FieldName          string  `db:"field_name"`
	NatureID           string  `db:"nature_id"`
	NatureName         string  `db:"nature_name"`
	ClinicianStatus    *string `db:"clinician_status"`
	AppointmentType    *string `db:"appointment_type"`
	Prices             *int    `db:"prices"`
	Duration           *int    `db:"duration"`
	PriceListType      *string `db:"price_list_type"`
	PriceType          *string `db:"price_type"`
	FieldStatusAcronym string  `db:"field_status_acronym"`
}

// GetBranchPriceConfigurationResult represents the result of the get operation
type GetBranchPriceConfigurationResult struct {
	Data []GetBranchPriceConfigurationModel
}

// GetBranchPriceConfigurations fetches branch price configurations with complex joins
func (r *BranchPriceConfigurationRepository) GetBranchPriceConfigurations(ctx context.Context, filter GetBranchPriceConfigurationFilter) (*GetBranchPriceConfigurationResult, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetBranchPriceConfigurations"), vlog.F("action", "get branch price configurations"))

	query := `
		SELECT DISTINCT
			a.id as area_id,
			a.label as area_label,
			f.uuid as field_id,
			f.name as field_name,
			n.uuid as nature_id,
			n.name as nature_name,
			bpc.clinician_status,
			bpc.appointment_type,
			bpc.prices,
			bpc.duration,
			bpc.price_list_type,
			bpc.price_type,
			fs.acronym as field_status_acronym
		FROM branch_team_member_fields btmf
		JOIN field f ON btmf.field_id = f.uuid
		JOIN area a ON f.area_uuid = a.id
		JOIN field_nature fn ON f.uuid = fn.field_uuid
		JOIN nature n ON fn.nature_uuid = n.uuid
		JOIN field_status fs ON btmf.field_status_id = fs.id
		LEFT JOIN branch_price_configuration bpc ON (
			bpc.branch_id = btmf.branch_id
			AND bpc.field_id = btmf.field_id
			AND bpc.nature_id = n.uuid
			AND bpc.clinician_status = fs.acronym
		)
		WHERE btmf.branch_id = $1
		AND btmf.enabled = true
		ORDER BY a.label, f.name, fn.position, fs.acronym
	`

	var models []GetBranchPriceConfigurationModel
	err := r.db.SelectContext(ctx, &models, query, filter.BranchID)
	if err != nil {
		logger.Error("failed to get branch price configurations", vlog.F("error", err), vlog.F("branch_id", filter.BranchID))
		return nil, err
	}

	logger.Info("successfully fetched branch price configurations",
		vlog.F("branch_id", filter.BranchID),
		vlog.F("count", len(models)))

	return &GetBranchPriceConfigurationResult{
		Data: models,
	}, nil
}
