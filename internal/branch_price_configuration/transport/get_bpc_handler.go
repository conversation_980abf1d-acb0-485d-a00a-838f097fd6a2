package transport

import (
	"context"
	"net/http"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/usecase"
)

type GetBranchPriceConfigurationUsecase interface {
	Execute(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error)
}

type GetBranchPriceConfigurationHandler struct {
	usecase   GetBranchPriceConfigurationUsecase
	validator *validator.Validate
}

type GetBranchPriceConfigurationResponse struct {
	Status   bool        `json:"status"`
	Message  string      `json:"message"`
	BranchID string      `json:"branch_id"`
	Data     interface{} `json:"data"`
}

func NewGetBranchPriceConfigurationHandler(usecase GetBranchPriceConfigurationUsecase) *GetBranchPriceConfigurationHandler {
	return &GetBranchPriceConfigurationHandler{
		usecase:   usecase,
		validator: validator.New(),
	}
}

func (h *GetBranchPriceConfigurationHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Use RequireRolesForBranch to check authorization
		adminInfo := authz.RequireRolesForBranch(ctx, "branch-owner", "super-admin", "branch-member")
		if adminInfo == nil {
			return c.JSON(http.StatusForbidden, GetBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Not authorized to perform this action",
				Data:    []interface{}{},
			})
		}

		branchID := c.Param("branch_id")
		if branchID == "" {
			return c.JSON(http.StatusBadRequest, GetBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Invalid branch ID format",
				Data:    []interface{}{},
			})
		}

		// Validate that the user can access this branch
		if adminInfo.BranchID.String() != branchID {
			return c.JSON(http.StatusForbidden, GetBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Not authorized to access this branch",
				Data:    []interface{}{},
			})
		}

		input := &usecase.GetBranchPriceConfigurationInput{
			BranchID: branchID,
		}

		// Validate input
		if err := h.validator.Struct(input); err != nil {
			return c.JSON(http.StatusBadRequest, GetBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Invalid request parameters",
				Data:    []interface{}{},
			})
		}

		output, err := h.usecase.Execute(ctx, input)
		if err != nil {
			// Check for specific error types
			if strings.Contains(err.Error(), "invalid input syntax for type uuid") {
				return c.JSON(http.StatusBadRequest, GetBranchPriceConfigurationResponse{
					Status:  false,
					Message: "Invalid branch ID format",
					Data:    []interface{}{},
				})
			}

			if strings.Contains(err.Error(), "no rows in result set") ||
				strings.Contains(strings.ToLower(err.Error()), "not found") {
				return c.JSON(http.StatusNotFound, GetBranchPriceConfigurationResponse{
					Status:  false,
					Message: "Branch price configurations not found",
					Data:    []interface{}{},
				})
			}

			return c.JSON(http.StatusInternalServerError, GetBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Internal server error",
				Data:    []interface{}{},
			})
		}

		return c.JSON(http.StatusOK, GetBranchPriceConfigurationResponse{
			Status:   true,
			Message:  "Branch price configurations fetched successfully.",
			BranchID: branchID,
			Data:     output.Data,
		})
	}
}
