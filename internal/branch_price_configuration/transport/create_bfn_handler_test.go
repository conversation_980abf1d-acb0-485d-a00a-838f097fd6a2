package transport

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/usecase"
)

// Updated mock repository to match new interface
// (returns *entity.BranchPriceConfiguration, error)
type mockBranchPriceConfigurationRepository struct {
	BatchCreateFunc func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error)
}

func (m *mockBranchPriceConfigurationRepository) BatchCreate(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
	if m.BatchCreateFunc != nil {
		return m.BatchCreateFunc(ctx, bfns)
	}
	return bfns, nil
}

// Use correct handler and request/response types
type testHandlerDeps struct {
	echo    *echo.Echo
	handler *CreateBranchPriceConfigurationHandler
	repo    *mockBranchPriceConfigurationRepository
}

func setupTestHandler() testHandlerDeps {
	e := echo.New()
	repo := &mockBranchPriceConfigurationRepository{}
	uc := usecase.NewBranchPriceConfigurationUsecase(repo)
	h := NewCreateBranchPriceConfigurationHandler(uc)
	return testHandlerDeps{e, h, repo}
}

func setMockPrincipal(c echo.Context, branchID, accountID, role string) {
	principal := authz.Principal{
		BranchID:  uuid.MustParse(branchID),
		AccountID: uuid.MustParse(accountID),
		Role:      role,
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))
}

func TestCreateBranchPriceConfigurationHandler_Success(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler
	repo := deps.repo

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	request := CreateBranchPriceConfigurationRequest{
		FieldIDs:          []string{fieldID},
		NatureIDs:         []string{natureID},
		ClinicianStatuses: []string{"active"},
		Prices:            1000,
		Duration:          30,
		AppointmentTypes:  []string{"in_clinic"},
		PriceListType:     "appointment",
		PriceType:         "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")

	repo.BatchCreateFunc = func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
		return bfns, nil
	}

	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.True(t, response["status"].(bool))
	assert.Equal(t, "Branch price configuration(s) created successfully.", response["message"])
	assert.NotEmpty(t, response["data"])
}

func TestCreateBranchPriceConfigurationHandler_InvalidRequest(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler

	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, core.NewID().Value(), core.NewID().Value(), "super-admin")

	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.NotEmpty(t, response["message"])
	assert.Empty(t, response["data"])
}

func TestCreateBranchPriceConfigurationHandler_InvalidDuration(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	request := CreateBranchPriceConfigurationRequest{
		FieldIDs:          []string{fieldID},
		NatureIDs:         []string{natureID},
		ClinicianStatuses: []string{"active"},
		Prices:            1000,
		Duration:          17, // Not a multiple of 15
		AppointmentTypes:  []string{"in_clinic"},
		PriceListType:     "appointment",
		PriceType:         "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")

	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.Equal(t, "Duration must be >0, <120, and a multiple of 15", response["message"])
	assert.Empty(t, response["data"])
}

func TestCreateBranchPriceConfigurationHandler_InvalidPriceListType(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler

	branchID := core.NewID().Value()
	accountUUID := core.NewID().Value()
	request := CreateBranchPriceConfigurationRequest{
		FieldIDs:          []string{core.NewID().Value()},
		NatureIDs:         []string{core.NewID().Value()},
		ClinicianStatuses: []string{"active"},
		Prices:            1000,
		Duration:          30,
		PriceListType:     "invalid",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")

	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.Equal(t, "Invalid request body", response["message"])
	assert.Empty(t, response["data"])
}

func TestCreateBranchPriceConfigurationHandler_UsecaseError(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler
	repo := deps.repo

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	request := CreateBranchPriceConfigurationRequest{
		FieldIDs:          []string{fieldID},
		NatureIDs:         []string{natureID},
		ClinicianStatuses: []string{"active"},
		Prices:            1000,
		Duration:          30,
		AppointmentTypes:  []string{"in_clinic"},
		PriceListType:     "appointment",
		PriceType:         "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")

	repo.BatchCreateFunc = func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
		return nil, errors.New("db error")
	}
	request.PriceListType = "appointment"
	jsonReq, _ = json.Marshal(request)
	req = httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp = httptest.NewRecorder()
	c = e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")
	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.Equal(t, "Failed to create branch field nature", response["message"])
	assert.Empty(t, response["data"])
}

func TestCreateBranchPriceConfigurationHandler_TreatmentInvalidAppointmentType(t *testing.T) {
	deps := setupTestHandler()
	e := deps.echo
	h := deps.handler

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	apptType := "live_latter" // Invalid for treatment
	request := CreateBranchPriceConfigurationRequest{
		FieldIDs:          []string{fieldID},
		NatureIDs:         []string{natureID},
		ClinicianStatuses: []string{"active"},
		Prices:            1000,
		Duration:          0,
		AppointmentTypes:  []string{apptType},
		PriceListType:     "treatment",
		PriceType:         "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-price-configurations", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	setMockPrincipal(c, branchID, accountUUID, "super-admin")

	err := h.Handle()(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.Equal(t, "Invalid appointment type. Treatment price list supports only In-Clinic appointments.", response["message"])
	assert.Empty(t, response["data"])
}
