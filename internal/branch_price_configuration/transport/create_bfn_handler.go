package transport

import (
	"context"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/usecase"
)

type CreateBranchPriceConfigurationUsecase interface {
	BatchExecute(ctx context.Context, input *usecase.BatchCreateBranchPriceConfigurationInput) (*usecase.BatchCreateBranchPriceConfigurationOutput, error)
}

type CreateBranchPriceConfigurationRequest struct {
	FieldIDs          []string `json:"field_ids" validate:"required,min=1"`
	NatureIDs         []string `json:"nature_ids" validate:"required,min=1"`
	ClinicianStatuses []string `json:"clinician_statuses" validate:"required,min=1"`
	Prices            int      `json:"prices" validate:"required"`
	Duration          int      `json:"duration"` // not required for Treatment type
	AppointmentTypes  []string `json:"appointment_types,omitempty" validate:"min=1"`
	PriceListType     string   `json:"price_list_type" validate:"required"`
	PriceType         string   `json:"price_type" validate:"required"`
}

type CreateBranchPriceConfigurationHandler struct {
	usecase CreateBranchPriceConfigurationUsecase
}

func NewCreateBranchPriceConfigurationHandler(usecase CreateBranchPriceConfigurationUsecase) *CreateBranchPriceConfigurationHandler {
	return &CreateBranchPriceConfigurationHandler{
		usecase: usecase,
	}
}

type CreateBranchPriceConfigurationResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func (h *CreateBranchPriceConfigurationHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		// Use RequireRolesForBranch to check authorization
		userInfo := authz.RequireRolesForBranch(ctx, "branch-owner", "super-admin")
		if userInfo == nil {
			return c.JSON(http.StatusForbidden, CreateBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Not authorized to perform this action",
				Data:    []interface{}{},
			})
		}

		var req CreateBranchPriceConfigurationRequest
		if err := c.Bind(&req); err != nil {
			return c.JSON(http.StatusBadRequest, CreateBranchPriceConfigurationResponse{
				Status:  false,
				Message: "invalid request body",
				Data:    []interface{}{},
			})
		}

		validate := validator.New()
		if err := validate.Struct(req); err != nil {
			return c.JSON(http.StatusBadRequest, CreateBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Invalid request body",
				Data:    []interface{}{},
			})
		}

		// Build batch input for all combinations
		batchInputs := []*usecase.CreateBranchPriceConfigurationInput{}
		validPriceTypes := map[string]bool{"fixed": true, "from": true}
		for _, fieldID := range req.FieldIDs {
			for _, natureID := range req.NatureIDs {
				for _, clinicianStatus := range req.ClinicianStatuses {
					for _, appointmentType := range req.AppointmentTypes {
						// Validate duration for appointment
						if req.PriceListType == "appointment" {
							if req.Duration >= 120 || req.Duration <= 0 || req.Duration%15 != 0 {
								return c.JSON(http.StatusBadRequest, CreateBranchPriceConfigurationResponse{
									Status:  false,
									Message: "Duration must be >0, <120, and a multiple of 15",
									Data:    []interface{}{},
								})
							}
						}
						// Validate price_type
						if !validPriceTypes[req.PriceType] {
							return c.JSON(http.StatusBadRequest, CreateBranchPriceConfigurationResponse{
								Status:  false,
								Message: "Invalid price_type",
								Data:    []interface{}{},
							})
						}
						// Validate appointment_type for treatment
						if req.PriceListType == "treatment" && appointmentType != "in_clinic" {
							return c.JSON(http.StatusBadRequest, CreateBranchPriceConfigurationResponse{
								Status:  false,
								Message: "Invalid appointment type. Treatment price list supports only In-Clinic appointments.",
								Data:    []interface{}{},
							})
						}
						batchInputs = append(batchInputs, &usecase.CreateBranchPriceConfigurationInput{
							BranchID:        userInfo.BranchID.String(),
							FieldID:         fieldID,
							NatureID:        natureID,
							ClinicianStatus: clinicianStatus,
							Prices:          req.Prices,
							Duration:        req.Duration,
							AppointmentType: &appointmentType,
							PriceListType:   req.PriceListType,
							PriceType:       req.PriceType,
						})
					}
				}
			}
		}
		batchInput := &usecase.BatchCreateBranchPriceConfigurationInput{Inputs: batchInputs}
		batchOutput, err := h.usecase.BatchExecute(c.Request().Context(), batchInput)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, CreateBranchPriceConfigurationResponse{
				Status:  false,
				Message: "Failed to create branch field nature",
				Data:    []interface{}{},
			})
		}
		return c.JSON(http.StatusCreated, CreateBranchPriceConfigurationResponse{
			Status:  true,
			Message: "Branch price configuration(s) created successfully.",
			Data:    batchOutput.Results,
		})
	}
}
