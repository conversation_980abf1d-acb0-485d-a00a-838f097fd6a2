package transport

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	authz "gitlab.viswalslab.com/backend/price-list/infra/server/middleware/authz"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/usecase"
)

type mockGetBranchPriceConfigurationUsecase struct {
	ExecuteFunc func(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error)
}

func (m *mockGetBranchPriceConfigurationUsecase) Execute(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error) {
	if m.ExecuteFunc != nil {
		return m.ExecuteFunc(ctx, input)
	}
	return &usecase.GetBranchPriceConfigurationOutput{Data: []usecase.GetBranchPriceConfigurationArea{}}, nil
}

func setupGetBranchPriceConfigurationHandler(mockUsecase *mockGetBranchPriceConfigurationUsecase) (*echo.Echo, *GetBranchPriceConfigurationHandler) {
	e := echo.New()
	handler := NewGetBranchPriceConfigurationHandler(mockUsecase)
	return e, handler
}

func TestGetBranchPriceConfigurationHandler_Success(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	branchID := uuid.New().String()

	// Mock successful usecase execution
	mockUsecase.ExecuteFunc = func(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error) {
		assert.Equal(t, branchID, input.BranchID)

		prices := 45
		duration := 30
		clinicianStatus := "master"
		appointmentType := "in_clinic"
		priceListType := "appointment"
		priceType := "fixed"

		return &usecase.GetBranchPriceConfigurationOutput{
			Data: []usecase.GetBranchPriceConfigurationArea{
				{
					AreaID:    "4beed17b-a38a-4da1-8b26-94d2f1513001",
					AreaLabel: "Dentistry",
					AreaData: []usecase.GetBranchPriceConfigurationField{
						{
							FieldID:              "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
							FieldName:            "Field Of Dentistry",
							FieldClinicianStatus: []string{"master", "elite", "pro"},
							FieldData: []usecase.GetBranchPriceConfigurationFieldData{
								{
									NatureID:        "171977db-0846-4dac-af9f-30d6a43f3dac",
									NatureName:      "Aesthetic Consultation",
									ClinicianStatus: &clinicianStatus,
									AppointmentType: &appointmentType,
									Prices:          &prices,
									Duration:        &duration,
									PriceListType:   &priceListType,
									PriceType:       &priceType,
								},
								{
									NatureID:   "5fc76a15-e2a9-4442-89ee-7cc8e1134de7",
									NatureName: "Bonding Consultation",
								},
							},
						},
					},
				},
			},
		}, nil
	}

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("branch_id")
	c.SetParamValues(branchID)

	// Mock principal in context
	branchUUID, _ := uuid.Parse(branchID)
	accountUUID := uuid.New()
	principal := authz.Principal{
		AccountID: accountUUID,
		BranchID:  branchUUID,
		Role:      "branch-owner",
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Status)
	assert.Equal(t, "Branch price configurations fetched successfully.", response.Message)
	assert.Equal(t, branchID, response.BranchID)
	assert.NotNil(t, response.Data)
}

func TestGetBranchPriceConfigurationHandler_MissingBranchID(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	// No branch_id param set

	// Mock principal in context
	branchUUID := uuid.New()
	accountUUID := uuid.New()
	principal := authz.Principal{
		AccountID: accountUUID,
		BranchID:  branchUUID,
		Role:      "branch-owner",
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Status)
	assert.Equal(t, "Invalid branch ID format", response.Message)
}

func TestGetBranchPriceConfigurationHandler_Unauthorized(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	branchID := uuid.New().String()

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("branch_id")
	c.SetParamValues(branchID)
	// No user info in context

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Status)
	assert.Equal(t, "Not authorized to perform this action", response.Message)
}

func TestGetBranchPriceConfigurationHandler_WrongBranch(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	branchID := uuid.New().String()
	differentBranchID := uuid.New().String()

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("branch_id")
	c.SetParamValues(branchID)

	// Mock principal with different branch ID
	branchUUID, _ := uuid.Parse(differentBranchID)
	accountUUID := uuid.New()
	principal := authz.Principal{
		AccountID: accountUUID,
		BranchID:  branchUUID,
		Role:      "branch-owner",
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Status)
	assert.Equal(t, "Not authorized to access this branch", response.Message)
}

func TestGetBranchPriceConfigurationHandler_UsecaseError(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	branchID := uuid.New().String()

	mockUsecase.ExecuteFunc = func(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error) {
		return nil, errors.New("database error")
	}

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("branch_id")
	c.SetParamValues(branchID)

	// Mock principal in context
	branchUUID, _ := uuid.Parse(branchID)
	accountUUID := uuid.New()
	principal := authz.Principal{
		AccountID: accountUUID,
		BranchID:  branchUUID,
		Role:      "branch-owner",
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Status)
	assert.Equal(t, "Internal server error", response.Message)
}

func TestGetBranchPriceConfigurationHandler_InvalidUUID(t *testing.T) {
	mockUsecase := &mockGetBranchPriceConfigurationUsecase{}
	e, handler := setupGetBranchPriceConfigurationHandler(mockUsecase)

	branchID := uuid.New().String()

	mockUsecase.ExecuteFunc = func(ctx context.Context, input *usecase.GetBranchPriceConfigurationInput) (*usecase.GetBranchPriceConfigurationOutput, error) {
		return nil, errors.New("invalid input syntax for type uuid")
	}

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("branch_id")
	c.SetParamValues(branchID)

	// Mock principal in context with the same branch ID to pass branch validation
	// This will allow us to test the usecase error handling for invalid UUID in database
	branchUUID, _ := uuid.Parse(branchID)
	accountUUID := uuid.New()
	principal := authz.Principal{
		AccountID: accountUUID,
		BranchID:  branchUUID,
		Role:      "branch-owner",
	}
	ctx := authz.WithPrincipal(c.Request().Context(), principal)
	c.SetRequest(c.Request().WithContext(ctx))

	err := handler.Handle()(c)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)

	var response GetBranchPriceConfigurationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Status)
	assert.Equal(t, "Invalid branch ID format", response.Message)
}
