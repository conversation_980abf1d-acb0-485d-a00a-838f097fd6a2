package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestBranchPriceConfiguration_Creation(t *testing.T) {
	t.Run("valid branch field nature creation", func(t *testing.T) {
		id := core.NewID()
		branchID := core.NewID()
		fieldID := core.NewID()
		natureID := core.NewID()
		createdAt, _ := core.NewTimestampFromTime(time.Now())
		updatedAt, _ := core.NewTimestampFromTime(time.Now())
		apptType := "in_clinic"
		bfn := BranchPriceConfiguration{
			ID:              *id,
			BranchID:        *branchID,
			FieldID:         *fieldID,
			NatureID:        *natureID,
			ClinicianStatus: "active",
			Prices:          1000,
			Duration:        30,
			AppointmentType: &apptType,
			CreatedDate:     *createdAt,
			UpdatedDate:     *updatedAt,
			PriceListType:   "treatment",
			PriceType:       "fixed",
		}
		assert.Equal(t, *id, bfn.ID)
		assert.Equal(t, *branchID, bfn.BranchID)
		assert.Equal(t, *fieldID, bfn.FieldID)
		assert.Equal(t, *natureID, bfn.NatureID)
		assert.Equal(t, "active", bfn.ClinicianStatus)
		assert.Equal(t, 1000, bfn.Prices)
		assert.Equal(t, 30, bfn.Duration)
		assert.Equal(t, &apptType, bfn.AppointmentType)
		assert.Equal(t, *createdAt, bfn.CreatedDate)
		assert.Equal(t, *updatedAt, bfn.UpdatedDate)
		assert.Equal(t, "treatment", bfn.PriceListType)
		assert.Equal(t, "fixed", bfn.PriceType)
	})

	t.Run("branch field nature with nil appointment type", func(t *testing.T) {
		id := core.NewID()
		bfn := BranchPriceConfiguration{
			ID:              *id,
			AppointmentType: nil,
		}
		assert.Equal(t, *id, bfn.ID)
		assert.Nil(t, bfn.AppointmentType)
	})
}

func TestBranchPriceConfiguration_NewBranchPriceConfiguration(t *testing.T) {
	apptType := "in_clinic"
	input := &BranchPriceConfigurationInput{
		BranchID:        core.NewID().Value(),
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
		PriceListType:   "appointment",
		PriceType:       "fixed",
	}
	bfn, err := NewBranchPriceConfiguration(input)
	assert.NoError(t, err)
	assert.Equal(t, input.ClinicianStatus, bfn.ClinicianStatus)
	assert.Equal(t, input.Prices, bfn.Prices)
	assert.Equal(t, input.Duration, bfn.Duration)
	assert.Equal(t, input.AppointmentType, bfn.AppointmentType)
	assert.Equal(t, input.PriceListType, bfn.PriceListType)
	assert.Equal(t, input.PriceType, bfn.PriceType)
	assert.NotEmpty(t, bfn.ID)
	assert.NotEmpty(t, bfn.CreatedDate)
	assert.NotEmpty(t, bfn.UpdatedDate)
}

func TestBranchPriceConfiguration_Getters(t *testing.T) {
	apptType := "in_clinic"
	input := &BranchPriceConfigurationInput{
		BranchID:        core.NewID().Value(),
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "pro",
		Prices:          500,
		Duration:        0,
		AppointmentType: &apptType,
		PriceListType:   "treatment",
		PriceType:       "from",
	}
	bfn, err := NewBranchPriceConfiguration(input)
	assert.NoError(t, err)
	assert.Equal(t, bfn.GetBranchID(), bfn.BranchID.Value())
	assert.Equal(t, bfn.GetFieldID(), bfn.FieldID.Value())
	assert.Equal(t, bfn.GetNatureID(), bfn.NatureID.Value())
	assert.Equal(t, bfn.GetClinicianStatus(), bfn.ClinicianStatus)
	assert.Equal(t, bfn.GetPrices(), bfn.Prices)
	assert.Equal(t, bfn.GetDuration(), bfn.Duration)
	assert.Equal(t, bfn.GetAppointmentType(), bfn.AppointmentType)
	assert.Equal(t, bfn.GetPriceListType(), bfn.PriceListType)
	assert.Equal(t, bfn.GetPriceType(), bfn.PriceType)
	assert.Equal(t, bfn.GetCreatedDate(), bfn.CreatedDate.String())
	assert.Equal(t, bfn.GetUpdatedDate(), bfn.UpdatedDate.String())
}
