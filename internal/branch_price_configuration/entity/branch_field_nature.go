package entity

import (
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
)

type BranchPriceConfiguration struct {
	ID              core.Identifier
	BranchID        core.Identifier
	FieldID         core.Identifier
	NatureID        core.Identifier
	ClinicianStatus string
	Prices          int
	Duration        int
	AppointmentType *string
	PriceListType   string // "appointment" or "treatment"
	PriceType       string // "fixed" or "from"
	CreatedDate     core.Timestamp
	UpdatedDate     core.Timestamp
}

type BranchPriceConfigurationInput struct {
	BranchID        string  `json:"branch_id"`
	FieldID         string  `json:"field_id"`
	NatureID        string  `json:"nature_id"`
	ClinicianStatus string  `json:"clinician_status"`
	Prices          int     `json:"prices"`
	Duration        int     `json:"duration"`
	AppointmentType *string `json:"appointment_type,omitempty"`
	PriceListType   string  `json:"price_list_type"`
	PriceType       string  `json:"price_type"`
}

func NewBranchPriceConfiguration(input *BranchPriceConfigurationInput) (*BranchPriceConfiguration, error) {
	if input == nil {
		return nil, fmt.Errorf("input cannot be nil")
	}
	branchID, err := core.NewIDFromString(input.BranchID)
	if err != nil {
		return nil, fmt.Errorf("invalid BranchID format: %w", err)
	}
	fieldID, err := core.NewIDFromString(input.FieldID)
	if err != nil {
		return nil, fmt.Errorf("invalid FieldID format: %w", err)
	}
	natureID, err := core.NewIDFromString(input.NatureID)
	if err != nil {
		return nil, fmt.Errorf("invalid NatureID format: %w", err)
	}

	id := core.NewID()
	now := core.NewTimestamp()
	return &BranchPriceConfiguration{
		ID:              *id,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: input.ClinicianStatus,
		Prices:          input.Prices,
		Duration:        input.Duration,
		AppointmentType: input.AppointmentType,
		PriceListType:   input.PriceListType,
		PriceType:       input.PriceType,
		CreatedDate:     now,
		UpdatedDate:     now,
	}, nil
}

// Getter methods for BranchPriceConfiguration fields
func (b *BranchPriceConfiguration) GetID() string               { return b.ID.Value() }
func (b *BranchPriceConfiguration) GetBranchID() string         { return b.BranchID.Value() }
func (b *BranchPriceConfiguration) GetFieldID() string          { return b.FieldID.Value() }
func (b *BranchPriceConfiguration) GetNatureID() string         { return b.NatureID.Value() }
func (b *BranchPriceConfiguration) GetClinicianStatus() string  { return b.ClinicianStatus }
func (b *BranchPriceConfiguration) GetPrices() int              { return b.Prices }
func (b *BranchPriceConfiguration) GetDuration() int            { return b.Duration }
func (b *BranchPriceConfiguration) GetAppointmentType() *string { return b.AppointmentType }
func (b *BranchPriceConfiguration) GetPriceListType() string    { return b.PriceListType }
func (b *BranchPriceConfiguration) GetPriceType() string        { return b.PriceType }
func (b *BranchPriceConfiguration) GetCreatedDate() string      { return b.CreatedDate.String() }
func (b *BranchPriceConfiguration) GetUpdatedDate() string      { return b.UpdatedDate.String() }
