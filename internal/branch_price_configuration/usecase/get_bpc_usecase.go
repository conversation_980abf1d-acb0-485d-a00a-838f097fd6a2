package usecase

import (
	"context"
	"sort"

	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var getBranchPriceConfigurationUsecaseName = "GetBranchPriceConfigurationUsecase"

type getBranchPriceConfigurationRepository interface {
	GetBranchPriceConfigurations(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error)
}

type GetBranchPriceConfigurationUsecase struct {
	repo getBranchPriceConfigurationRepository
}

type GetBranchPriceConfigurationInput struct {
	BranchID string `validate:"required"`
}

type GetBranchPriceConfigurationFieldData struct {
	NatureID        string  `json:"nature_id"`
	NatureName      string  `json:"nature_name"`
	ClinicianStatus *string `json:"clinician_status,omitempty"`
	AppointmentType *string `json:"appointment_type,omitempty"`
	Prices          *int    `json:"prices,omitempty"`
	Duration        *int    `json:"duration,omitempty"`
	PriceListType   *string `json:"price_list_type,omitempty"`
	PriceType       *string `json:"price_type,omitempty"`
}

type GetBranchPriceConfigurationField struct {
	FieldID              string                                 `json:"field_id"`
	FieldName            string                                 `json:"field_name"`
	FieldClinicianStatus []string                               `json:"field_clinician_status"`
	FieldData            []GetBranchPriceConfigurationFieldData `json:"field_data"`
}

type GetBranchPriceConfigurationArea struct {
	AreaID    string                             `json:"area_id"`
	AreaLabel string                             `json:"area_label"`
	AreaData  []GetBranchPriceConfigurationField `json:"area_data"`
}

type GetBranchPriceConfigurationOutput struct {
	Data []GetBranchPriceConfigurationArea `json:"data"`
}

func NewGetBranchPriceConfigurationUsecase(repo getBranchPriceConfigurationRepository) *GetBranchPriceConfigurationUsecase {
	return &GetBranchPriceConfigurationUsecase{
		repo: repo,
	}
}

func (u *GetBranchPriceConfigurationUsecase) Execute(ctx context.Context, input *GetBranchPriceConfigurationInput) (*GetBranchPriceConfigurationOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", getBranchPriceConfigurationUsecaseName))
	logger.Info("Executing GetBranchPriceConfigurationUsecase")

	filter := repository.GetBranchPriceConfigurationFilter{
		BranchID: input.BranchID,
	}

	result, err := u.repo.GetBranchPriceConfigurations(ctx, filter)
	if err != nil {
		logger.Error("failed to get branch price configurations", vlog.F("error", err))
		return nil, err
	}

	// Group data by area, then by field, then by nature
	areaMap := make(map[string]*GetBranchPriceConfigurationArea)
	fieldMap := make(map[string]*GetBranchPriceConfigurationField)
	fieldStatusMap := make(map[string]map[string]bool) // field_id -> status -> exists

	for _, model := range result.Data {
		// Initialize area if not exists
		if _, exists := areaMap[model.AreaID]; !exists {
			areaMap[model.AreaID] = &GetBranchPriceConfigurationArea{
				AreaID:    model.AreaID,
				AreaLabel: model.AreaLabel,
				AreaData:  []GetBranchPriceConfigurationField{},
			}
		}

		// Initialize field if not exists
		fieldKey := model.AreaID + "_" + model.FieldID
		if _, exists := fieldMap[fieldKey]; !exists {
			fieldMap[fieldKey] = &GetBranchPriceConfigurationField{
				FieldID:              model.FieldID,
				FieldName:            model.FieldName,
				FieldClinicianStatus: []string{},
				FieldData:            []GetBranchPriceConfigurationFieldData{},
			}
		}

		// Track field statuses
		if _, exists := fieldStatusMap[model.FieldID]; !exists {
			fieldStatusMap[model.FieldID] = make(map[string]bool)
		}
		fieldStatusMap[model.FieldID][model.FieldStatusAcronym] = true

		// Add nature data
		fieldData := GetBranchPriceConfigurationFieldData{
			NatureID:        model.NatureID,
			NatureName:      model.NatureName,
			ClinicianStatus: model.ClinicianStatus,
			AppointmentType: model.AppointmentType,
			Prices:          model.Prices,
			Duration:        model.Duration,
			PriceListType:   model.PriceListType,
			PriceType:       model.PriceType,
		}

		fieldMap[fieldKey].FieldData = append(fieldMap[fieldKey].FieldData, fieldData)
	}

	// Populate field clinician statuses
	for _, field := range fieldMap {
		fieldID := field.FieldID
		if statuses, exists := fieldStatusMap[fieldID]; exists {
			for status := range statuses {
				field.FieldClinicianStatus = append(field.FieldClinicianStatus, status)
			}
			// Sort statuses for consistent output
			sort.Strings(field.FieldClinicianStatus)
		}
	}

	// Group fields by area
	for fieldKey, field := range fieldMap {
		areaID := fieldKey[:len(fieldKey)-len(field.FieldID)-1] // Extract area ID from fieldKey
		if area, exists := areaMap[areaID]; exists {
			area.AreaData = append(area.AreaData, *field)
		}
	}

	// Convert map to slice and sort
	var areas []GetBranchPriceConfigurationArea
	for _, area := range areaMap {
		// Sort fields within area
		sort.Slice(area.AreaData, func(i, j int) bool {
			return area.AreaData[i].FieldName < area.AreaData[j].FieldName
		})
		areas = append(areas, *area)
	}

	// Sort areas
	sort.Slice(areas, func(i, j int) bool {
		return areas[i].AreaLabel < areas[j].AreaLabel
	})

	return &GetBranchPriceConfigurationOutput{
		Data: areas,
	}, nil
}
