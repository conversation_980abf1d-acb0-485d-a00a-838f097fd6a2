package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var createBranchPriceConfigurationUsecaseName = "CreateBranchPriceConfigurationUsecase"

type createBranchPriceConfigurationRepository interface {
	BatchCreate(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error)
}
type CreateBranchPriceConfigurationUsecase struct {
	repo createBranchPriceConfigurationRepository
}

type CreateBranchPriceConfigurationInput struct {
	BranchID        string
	FieldID         string
	NatureID        string
	ClinicianStatus string
	Prices          int
	Duration        int
	AppointmentType *string
	PriceListType   string // "appointment" or "treatment"
	PriceType       string // "fixed" or "from"
}

type CreateBranchPriceConfigurationOutputModel struct {
	ID              string
	BranchID        string
	FieldID         string
	NatureID        string
	ClinicianStatus string
	Prices          int
	Duration        int
	AppointmentType *string
	PriceListType   string // "appointment" or "treatment"
	PriceType       string // "fixed" or "from"
	CreatedDate     string
	UpdatedDate     string
}

type CreateBranchPriceConfigurationOutput struct {
	Data *CreateBranchPriceConfigurationOutputModel
}

type BatchCreateBranchPriceConfigurationInput struct {
	Inputs []*CreateBranchPriceConfigurationInput
}

type BatchCreateBranchPriceConfigurationOutput struct {
	Results []*CreateBranchPriceConfigurationOutputModel
}

func NewBranchPriceConfigurationUsecase(repo createBranchPriceConfigurationRepository) *CreateBranchPriceConfigurationUsecase {
	return &CreateBranchPriceConfigurationUsecase{
		repo: repo,
	}
}

func (u *CreateBranchPriceConfigurationUsecase) BatchExecute(ctx context.Context, batchInput *BatchCreateBranchPriceConfigurationInput) (*BatchCreateBranchPriceConfigurationOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", createBranchPriceConfigurationUsecaseName))
	logger.Info("Executing CreateBranchPriceConfigurationUsecase")
	var entities []*entity.BranchPriceConfiguration
	for _, input := range batchInput.Inputs {
		bfnInput := &entity.BranchPriceConfigurationInput{
			BranchID:        input.BranchID,
			FieldID:         input.FieldID,
			NatureID:        input.NatureID,
			ClinicianStatus: input.ClinicianStatus,
			Prices:          input.Prices,
			Duration:        input.Duration,
			AppointmentType: input.AppointmentType,
			PriceListType:   input.PriceListType,
			PriceType:       input.PriceType,
		}
		bfn, err := entity.NewBranchPriceConfiguration(bfnInput)
		if err != nil {
			return nil, err
		}
		entities = append(entities, bfn)
	}
	createdBFNs, err := u.repo.BatchCreate(ctx, entities)
	if err != nil {
		return nil, err
	}
	var results []*CreateBranchPriceConfigurationOutputModel
	for _, createdBFN := range createdBFNs {
		outputModel := &CreateBranchPriceConfigurationOutputModel{
			ID:              createdBFN.GetID(),
			BranchID:        createdBFN.GetBranchID(),
			FieldID:         createdBFN.GetFieldID(),
			NatureID:        createdBFN.GetNatureID(),
			ClinicianStatus: createdBFN.GetClinicianStatus(),
			Prices:          createdBFN.GetPrices(),
			Duration:        createdBFN.GetDuration(),
			AppointmentType: createdBFN.GetAppointmentType(),
			PriceListType:   createdBFN.GetPriceListType(),
			PriceType:       createdBFN.GetPriceType(),
			CreatedDate:     createdBFN.GetCreatedDate(),
			UpdatedDate:     createdBFN.GetUpdatedDate(),
		}
		results = append(results, outputModel)
	}
	return &BatchCreateBranchPriceConfigurationOutput{Results: results}, nil
}
