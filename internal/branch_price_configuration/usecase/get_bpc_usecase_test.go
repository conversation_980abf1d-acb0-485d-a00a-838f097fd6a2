package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/repository"
)

type mockGetBranchPriceConfigurationRepository struct {
	GetBranchPriceConfigurationsFunc func(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error)
}

func (m *mockGetBranchPriceConfigurationRepository) GetBranchPriceConfigurations(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error) {
	if m.GetBranchPriceConfigurationsFunc != nil {
		return m.GetBranchPriceConfigurationsFunc(ctx, filter)
	}
	return &repository.GetBranchPriceConfigurationResult{Data: []repository.GetBranchPriceConfigurationModel{}}, nil
}

func TestGetBranchPriceConfigurationUsecase_Success(t *testing.T) {
	mock := &mockGetBranchPriceConfigurationRepository{}
	uc := NewGetBranchPriceConfigurationUsecase(mock)

	branchID := "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
	input := &GetBranchPriceConfigurationInput{
		BranchID: branchID,
	}

	// Mock repository response with test data
	masterStatus := "master"
	appointmentType := "in_clinic"
	prices := 45
	duration := 30
	priceListType := "appointment"
	priceType := "fixed"

	mock.GetBranchPriceConfigurationsFunc = func(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error) {
		assert.Equal(t, branchID, filter.BranchID)
		return &repository.GetBranchPriceConfigurationResult{
			Data: []repository.GetBranchPriceConfigurationModel{
				{
					AreaID:             "4beed17b-a38a-4da1-8b26-94d2f1513001",
					AreaLabel:          "Dentistry",
					FieldID:            "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
					FieldName:          "Field Of Dentistry",
					NatureID:           "171977db-0846-4dac-af9f-30d6a43f3dac",
					NatureName:         "Aesthetic Consultation",
					ClinicianStatus:    &masterStatus,
					AppointmentType:    &appointmentType,
					Prices:             &prices,
					Duration:           &duration,
					PriceListType:      &priceListType,
					PriceType:          &priceType,
					FieldStatusAcronym: "master",
				},
				{
					AreaID:             "4beed17b-a38a-4da1-8b26-94d2f1513001",
					AreaLabel:          "Dentistry",
					FieldID:            "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
					FieldName:          "Field Of Dentistry",
					NatureID:           "5fc76a15-e2a9-4442-89ee-7cc8e1134de7",
					NatureName:         "Bonding Consultation",
					ClinicianStatus:    nil,
					AppointmentType:    nil,
					Prices:             nil,
					Duration:           nil,
					PriceListType:      nil,
					PriceType:          nil,
					FieldStatusAcronym: "elite",
				},
			},
		}, nil
	}

	output, err := uc.Execute(context.Background(), input)

	assert.NoError(t, err)
	assert.NotNil(t, output)
	assert.Len(t, output.Data, 1) // One area

	// Check area structure
	area := output.Data[0]
	assert.Equal(t, "4beed17b-a38a-4da1-8b26-94d2f1513001", area.AreaID)
	assert.Equal(t, "Dentistry", area.AreaLabel)
	assert.Len(t, area.AreaData, 1) // One field

	// Check field structure
	field := area.AreaData[0]
	assert.Equal(t, "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75", field.FieldID)
	assert.Equal(t, "Field Of Dentistry", field.FieldName)
	assert.Contains(t, field.FieldClinicianStatus, "master")
	assert.Contains(t, field.FieldClinicianStatus, "elite")
	assert.Len(t, field.FieldData, 2) // Two natures

	// Check first nature (with price configuration)
	nature1 := field.FieldData[0]
	assert.Equal(t, "171977db-0846-4dac-af9f-30d6a43f3dac", nature1.NatureID)
	assert.Equal(t, "Aesthetic Consultation", nature1.NatureName)
	assert.NotNil(t, nature1.ClinicianStatus)
	assert.Equal(t, "master", *nature1.ClinicianStatus)
	assert.NotNil(t, nature1.AppointmentType)
	assert.Equal(t, "in_clinic", *nature1.AppointmentType)
	assert.NotNil(t, nature1.Prices)
	assert.Equal(t, 45, *nature1.Prices)
	assert.NotNil(t, nature1.Duration)
	assert.Equal(t, 30, *nature1.Duration)
	assert.NotNil(t, nature1.PriceListType)
	assert.Equal(t, "appointment", *nature1.PriceListType)
	assert.NotNil(t, nature1.PriceType)
	assert.Equal(t, "fixed", *nature1.PriceType)

	// Check second nature (without price configuration)
	nature2 := field.FieldData[1]
	assert.Equal(t, "5fc76a15-e2a9-4442-89ee-7cc8e1134de7", nature2.NatureID)
	assert.Equal(t, "Bonding Consultation", nature2.NatureName)
	assert.Nil(t, nature2.ClinicianStatus)
	assert.Nil(t, nature2.AppointmentType)
	assert.Nil(t, nature2.Prices)
	assert.Nil(t, nature2.Duration)
	assert.Nil(t, nature2.PriceListType)
	assert.Nil(t, nature2.PriceType)
}

func TestGetBranchPriceConfigurationUsecase_EmptyResult(t *testing.T) {
	mock := &mockGetBranchPriceConfigurationRepository{}
	uc := NewGetBranchPriceConfigurationUsecase(mock)

	input := &GetBranchPriceConfigurationInput{
		BranchID: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
	}

	mock.GetBranchPriceConfigurationsFunc = func(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error) {
		return &repository.GetBranchPriceConfigurationResult{
			Data: []repository.GetBranchPriceConfigurationModel{},
		}, nil
	}

	output, err := uc.Execute(context.Background(), input)

	assert.NoError(t, err)
	assert.NotNil(t, output)
	assert.Len(t, output.Data, 0)
}

func TestGetBranchPriceConfigurationUsecase_RepositoryError(t *testing.T) {
	mock := &mockGetBranchPriceConfigurationRepository{}
	uc := NewGetBranchPriceConfigurationUsecase(mock)

	input := &GetBranchPriceConfigurationInput{
		BranchID: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
	}

	mock.GetBranchPriceConfigurationsFunc = func(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error) {
		return nil, errors.New("database error")
	}

	output, err := uc.Execute(context.Background(), input)

	assert.Error(t, err)
	assert.Nil(t, output)
	assert.Contains(t, err.Error(), "database error")
}

func TestGetBranchPriceConfigurationUsecase_MultipleAreasAndFields(t *testing.T) {
	mock := &mockGetBranchPriceConfigurationRepository{}
	uc := NewGetBranchPriceConfigurationUsecase(mock)

	input := &GetBranchPriceConfigurationInput{
		BranchID: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
	}

	mock.GetBranchPriceConfigurationsFunc = func(ctx context.Context, filter repository.GetBranchPriceConfigurationFilter) (*repository.GetBranchPriceConfigurationResult, error) {
		return &repository.GetBranchPriceConfigurationResult{
			Data: []repository.GetBranchPriceConfigurationModel{
				{
					AreaID:             "4beed17b-a38a-4da1-8b26-94d2f1513001",
					AreaLabel:          "Dentistry",
					FieldID:            "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
					FieldName:          "Field Of Dentistry",
					NatureID:           "171977db-0846-4dac-af9f-30d6a43f3dac",
					NatureName:         "Aesthetic Consultation",
					FieldStatusAcronym: "master",
				},
				{
					AreaID:             "84ce4d70-a442-412b-bf27-06f4544a8661",
					AreaLabel:          "Medicine",
					FieldID:            "1be15415-015a-462f-b6ac-e83a3e09dadf",
					FieldName:          "Ophthalmology",
					NatureID:           "567b6d17-1733-476d-bea9-16eeb967a5a0",
					NatureName:         "Dental Consultation",
					FieldStatusAcronym: "pro",
				},
			},
		}, nil
	}

	output, err := uc.Execute(context.Background(), input)

	assert.NoError(t, err)
	assert.NotNil(t, output)
	assert.Len(t, output.Data, 2) // Two areas

	// Areas should be sorted by label
	assert.Equal(t, "Dentistry", output.Data[0].AreaLabel)
	assert.Equal(t, "Medicine", output.Data[1].AreaLabel)
}
