package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_price_configuration/entity"
)

type mockBatchRepo struct {
	BatchCreateFunc func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error)
}

func (m *mockBatchRepo) BatchCreate(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
	if m.BatchCreateFunc != nil {
		return m.BatchCreateFunc(ctx, bfns)
	}
	return bfns, nil
}

func newTestInput() *CreateBranchPriceConfigurationInput {
	return &CreateBranchPriceConfigurationInput{
		BranchID:        "b1b2c3d4-e5f6-7a8b-9c0d-e1f2a3b4c5d6",
		FieldID:         "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75",
		NatureID:        "171977db-0846-4dac-af9f-30d6a43f3dac",
		ClinicianStatus: "master",
		Prices:          45,
		Duration:        30,
		AppointmentType: nil,
		PriceListType:   "appointment",
		PriceType:       "fixed",
	}
}

func TestBatchCreateBranchPriceConfigurationUsecase_Success(t *testing.T) {
	mock := &mockBatchRepo{}
	uc := NewBranchPriceConfigurationUsecase(mock)
	input := &BatchCreateBranchPriceConfigurationInput{
		Inputs: []*CreateBranchPriceConfigurationInput{newTestInput(), newTestInput()},
	}
	mock.BatchCreateFunc = func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
		return bfns, nil
	}
	output, err := uc.BatchExecute(context.Background(), input)
	assert.NoError(t, err)
	assert.Len(t, output.Results, 2)
	for _, result := range output.Results {
		assert.Equal(t, input.Inputs[0].BranchID, result.BranchID)
		assert.Equal(t, input.Inputs[0].FieldID, result.FieldID)
		assert.Equal(t, input.Inputs[0].NatureID, result.NatureID)
		assert.Equal(t, input.Inputs[0].ClinicianStatus, result.ClinicianStatus)
		assert.Equal(t, input.Inputs[0].Prices, result.Prices)
		assert.Equal(t, input.Inputs[0].Duration, result.Duration)
		assert.Equal(t, input.Inputs[0].PriceListType, result.PriceListType)
		assert.Equal(t, input.Inputs[0].PriceType, result.PriceType)
	}
}

func TestBatchCreateBranchPriceConfigurationUsecase_EntityError(t *testing.T) {
	mock := &mockBatchRepo{}
	uc := NewBranchPriceConfigurationUsecase(mock)
	input := &BatchCreateBranchPriceConfigurationInput{
		Inputs: []*CreateBranchPriceConfigurationInput{{}},
	}
	output, err := uc.BatchExecute(context.Background(), input)
	assert.Error(t, err)
	assert.Nil(t, output)
}

func TestBatchCreateBranchPriceConfigurationUsecase_RepoError(t *testing.T) {
	mock := &mockBatchRepo{}
	uc := NewBranchPriceConfigurationUsecase(mock)
	input := &BatchCreateBranchPriceConfigurationInput{
		Inputs: []*CreateBranchPriceConfigurationInput{newTestInput()},
	}
	mock.BatchCreateFunc = func(ctx context.Context, bfns []*entity.BranchPriceConfiguration) ([]*entity.BranchPriceConfiguration, error) {
		return nil, errors.New("db error")
	}
	output, err := uc.BatchExecute(context.Background(), input)
	assert.Error(t, err)
	assert.Nil(t, output)
}
