package BranchPriceConfiguration

import (
	"testing"

	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type mockLogger struct{}

func (m *mockLogger) Debug(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Info(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Warn(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Error(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Fatal(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Panic(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Flush() error {
	return nil
}
func (m *mockLogger) With(fields ...vlog.Field) vlog.Logger {
	return m
}
func (m *mockLogger) Log(level vlog.Level, msg string, fields ...vlog.Field) {}

func TestNewFactory(t *testing.T) {
	db := &sqlx.DB{} // nil DB is fine for constructor test
	logger := &mockLogger{}
	factory := NewFactory(db, logger)
	assert.NotNil(t, factory)
	assert.Equal(t, db, factory.db)
	assert.Equal(t, logger, factory.logger)
}

func TestFactory_CreateBranchPriceConfigurationHandler(t *testing.T) {
	db := &sqlx.DB{} // nil DB is fine for handler wiring test
	logger := &mockLogger{}
	factory := NewFactory(db, logger)
	handler := factory.CreateBranchPriceConfigurationHandler()
	assert.NotNil(t, handler)
}
