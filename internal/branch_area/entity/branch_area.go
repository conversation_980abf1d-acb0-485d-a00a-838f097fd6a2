package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// BranchArea represents a mapping between a branch and area
type BranchArea struct {
	id        core.Identifier
	branchID  core.Identifier
	areaID    core.Identifier
	createdAt core.Timestamp
	updatedAt core.Timestamp
}

// NewBranchAreaInput contains data for creating a new branch area mapping
type NewBranchAreaInput struct {
	BranchID  string
	AreaID    string
	CreatedAt *time.Time
	UpdatedAt *time.Time
}

func NewBranchArea(input *NewBranchAreaInput) (*BranchArea, error) {
	if input == nil {
		panic("NewBranchAreaInput cannot be nil")
	}

	id := core.NewID()

	branchID, err := core.NewIDFromString(input.BranchID)
	if err != nil {
		return nil, err
	}

	areaID, err := core.NewIDFromString(input.AreaID)
	if err != nil {
		return nil, err
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, err
	}

	return &BranchArea{
		id:        *id,
		branchID:  *branchID,
		areaID:    *areaID,
		createdAt: *createdAt,
		updatedAt: *updatedAt,
	}, nil
}

// BranchArea getter methods
func (ba BranchArea) ID() string {
	return ba.id.Value()
}

func (ba BranchArea) BranchID() string {
	return ba.branchID.Value()
}

func (ba BranchArea) AreaID() string {
	return ba.areaID.Value()
}

func (ba BranchArea) CreatedAt() time.Time {
	return ba.createdAt.Value()
}

func (ba BranchArea) UpdatedAt() time.Time {
	return ba.updatedAt.Value()
}

// BranchTeamMemberRole represents a mapping between a branch and team member (without role)
type BranchTeamMemberRole struct {
	id        core.Identifier
	branchID  core.Identifier
	accountID core.Identifier
	createdAt core.Timestamp
	updatedAt core.Timestamp
}

// NewBranchTeamMemberRoleInput contains data for creating a new branch team member role mapping
type NewBranchTeamMemberRoleInput struct {
	BranchID  string
	AccountID string
	CreatedAt *time.Time
	UpdatedAt *time.Time
}

func NewBranchTeamMemberRole(input *NewBranchTeamMemberRoleInput) (*BranchTeamMemberRole, error) {
	if input == nil {
		panic("NewBranchTeamMemberRoleInput cannot be nil")
	}

	id := core.NewID()

	branchID, err := core.NewIDFromString(input.BranchID)
	if err != nil {
		return nil, err
	}

	accountID, err := core.NewIDFromString(input.AccountID)
	if err != nil {
		return nil, err
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, err
	}

	return &BranchTeamMemberRole{
		id:        *id,
		branchID:  *branchID,
		accountID: *accountID,
		createdAt: *createdAt,
		updatedAt: *updatedAt,
	}, nil
}

// BranchTeamMemberRole getter methods
func (btmr BranchTeamMemberRole) ID() string {
	return btmr.id.Value()
}

func (btmr BranchTeamMemberRole) BranchID() string {
	return btmr.branchID.Value()
}

func (btmr BranchTeamMemberRole) AccountID() string {
	return btmr.accountID.Value()
}

func (btmr BranchTeamMemberRole) CreatedAt() time.Time {
	return btmr.createdAt.Value()
}

func (btmr BranchTeamMemberRole) UpdatedAt() time.Time {
	return btmr.updatedAt.Value()
}
