package entity_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
)

var validBranchID = core.NewID().Value()
var validAreaID = core.NewID().Value()

func TestNewBranchArea(t *testing.T) {
	tests := []struct {
		name    string
		input   *entity.NewBranchAreaInput
		wantErr bool
	}{
		{
			name: "Valid input with all fields",
			input: &entity.NewBranchAreaInput{
				BranchID:  validBranchID,
				AreaID:    validAreaID,
				CreatedAt: core.ToPtr(time.Now()),
				UpdatedAt: core.ToPtr(time.Now()),
			},
			wantErr: false,
		},
		{
			name: "Valid input without timestamps",
			input: &entity.NewBranchAreaInput{
				BranchID: validBranchID,
				AreaID:   validAreaID,
			},
			wantErr: false,
		},
		{
			name: "Invalid branch ID format",
			input: &entity.NewBranchAreaInput{
				BranchID: "invalid-id-format",
				AreaID:   validAreaID,
			},
			wantErr: true,
		},
		{
			name: "Invalid area ID format",
			input: &entity.NewBranchAreaInput{
				BranchID: validBranchID,
				AreaID:   "invalid-id-format",
			},
			wantErr: true,
		},
		{
			name: "Empty branch ID",
			input: &entity.NewBranchAreaInput{
				BranchID: "",
				AreaID:   validAreaID,
			},
			wantErr: true,
		},
		{
			name: "Empty area ID",
			input: &entity.NewBranchAreaInput{
				BranchID: validBranchID,
				AreaID:   "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			branchArea, err := entity.NewBranchArea(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, branchArea)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, branchArea)

				// Verify all getter methods work correctly
				assert.Equal(t, tt.input.BranchID, branchArea.BranchID())
				assert.Equal(t, tt.input.AreaID, branchArea.AreaID())

				// Verify timestamps are set (either from input or default)
				assert.NotZero(t, branchArea.CreatedAt())
				assert.NotZero(t, branchArea.UpdatedAt())

				if tt.input.CreatedAt != nil {
					assert.Equal(t, tt.input.CreatedAt.UTC(), branchArea.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), branchArea.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestBranchAreaGetters(t *testing.T) {
	now := time.Now()
	input := &entity.NewBranchAreaInput{
		BranchID:  validBranchID,
		AreaID:    validAreaID,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	branchArea, err := entity.NewBranchArea(input)
	assert.NoError(t, err)
	assert.NotNil(t, branchArea)

	// Test all getter methods
	assert.Equal(t, input.BranchID, branchArea.BranchID())
	assert.Equal(t, input.AreaID, branchArea.AreaID())
	assert.Equal(t, now.UTC(), branchArea.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), branchArea.UpdatedAt().UTC())
}

func TestBranchAreaWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &entity.NewBranchAreaInput{
		BranchID:  validBranchID,
		AreaID:    validAreaID,
		CreatedAt: &createdTime,
		UpdatedAt: &updatedTime,
	}

	branchArea, err := entity.NewBranchArea(input)
	assert.NoError(t, err)
	assert.NotNil(t, branchArea)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), branchArea.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), branchArea.UpdatedAt().UTC())
	assert.True(t, branchArea.CreatedAt().Before(branchArea.UpdatedAt()))
}

func TestBranchAreaWithNilTimestamps(t *testing.T) {
	input := &entity.NewBranchAreaInput{
		BranchID:  validBranchID,
		AreaID:    validAreaID,
		CreatedAt: nil,
		UpdatedAt: nil,
	}

	beforeCreate := time.Now()
	branchArea, err := entity.NewBranchArea(input)
	afterCreate := time.Now()

	assert.NoError(t, err)
	assert.NotNil(t, branchArea)

	// Verify default timestamps are set to current time
	assert.True(t, branchArea.CreatedAt().After(beforeCreate) || branchArea.CreatedAt().Equal(beforeCreate))
	assert.True(t, branchArea.CreatedAt().Before(afterCreate) || branchArea.CreatedAt().Equal(afterCreate))
	assert.True(t, branchArea.UpdatedAt().After(beforeCreate) || branchArea.UpdatedAt().Equal(beforeCreate))
	assert.True(t, branchArea.UpdatedAt().Before(afterCreate) || branchArea.UpdatedAt().Equal(afterCreate))
}

func TestNewBranchTeamMemberRole(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()
	now := time.Now().UTC()

	tests := []struct {
		name           string
		input          *entity.NewBranchTeamMemberRoleInput
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Valid input with all fields",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  validBranchID.Value(),
				AccountID: validAccountID.Value(),
				CreatedAt: &now,
				UpdatedAt: &now,
			},
			wantErr: false,
		},
		{
			name: "Valid input without timestamps",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  validBranchID.Value(),
				AccountID: validAccountID.Value(),
			},
			wantErr: false,
		},
		{
			name: "Invalid branch ID format",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  "invalid-uuid",
				AccountID: validAccountID.Value(),
			},
			wantErr:        true,
			wantErrMessage: "invalid UUID",
		},
		{
			name: "Invalid account ID format",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  validBranchID.Value(),
				AccountID: "invalid-uuid",
			},
			wantErr:        true,
			wantErrMessage: "invalid UUID",
		},
		{
			name: "Empty branch ID",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  "",
				AccountID: validAccountID.Value(),
			},
			wantErr:        true,
			wantErrMessage: "invalid identifier",
		},
		{
			name: "Empty account ID",
			input: &entity.NewBranchTeamMemberRoleInput{
				BranchID:  validBranchID.Value(),
				AccountID: "",
			},
			wantErr:        true,
			wantErrMessage: "invalid identifier",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := entity.NewBranchTeamMemberRole(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify the entity was created correctly
				assert.NotEmpty(t, result.ID())
				assert.Equal(t, tt.input.BranchID, result.BranchID())
				assert.Equal(t, tt.input.AccountID, result.AccountID())
				assert.False(t, result.CreatedAt().IsZero())
				assert.False(t, result.UpdatedAt().IsZero())

				// If timestamps were provided, verify they were used
				if tt.input.CreatedAt != nil {
					assert.Equal(t, *tt.input.CreatedAt, result.CreatedAt())
				}
				if tt.input.UpdatedAt != nil {
					assert.Equal(t, *tt.input.UpdatedAt, result.UpdatedAt())
				}
			}
		})
	}
}

func TestBranchTeamMemberRoleGetters(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()
	now := time.Now().UTC()

	input := &entity.NewBranchTeamMemberRoleInput{
		BranchID:  validBranchID.Value(),
		AccountID: validAccountID.Value(),
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	branchTeamMemberRole, err := entity.NewBranchTeamMemberRole(input)
	require.NoError(t, err)

	// Test all getter methods
	assert.NotEmpty(t, branchTeamMemberRole.ID())
	assert.Equal(t, validBranchID.Value(), branchTeamMemberRole.BranchID())
	assert.Equal(t, validAccountID.Value(), branchTeamMemberRole.AccountID())
	assert.Equal(t, now, branchTeamMemberRole.CreatedAt())
	assert.Equal(t, now, branchTeamMemberRole.UpdatedAt())
}

func TestBranchTeamMemberRoleWithDifferentTimestamps(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()
	createdTime := time.Now().UTC().Add(-1 * time.Hour)
	updatedTime := time.Now().UTC()

	input := &entity.NewBranchTeamMemberRoleInput{
		BranchID:  validBranchID.Value(),
		AccountID: validAccountID.Value(),
		CreatedAt: &createdTime,
		UpdatedAt: &updatedTime,
	}

	branchTeamMemberRole, err := entity.NewBranchTeamMemberRole(input)
	require.NoError(t, err)

	assert.Equal(t, createdTime, branchTeamMemberRole.CreatedAt())
	assert.Equal(t, updatedTime, branchTeamMemberRole.UpdatedAt())
	assert.True(t, branchTeamMemberRole.UpdatedAt().After(branchTeamMemberRole.CreatedAt()))
}

func TestBranchTeamMemberRoleWithNilTimestamps(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()

	input := &entity.NewBranchTeamMemberRoleInput{
		BranchID:  validBranchID.Value(),
		AccountID: validAccountID.Value(),
		CreatedAt: nil,
		UpdatedAt: nil,
	}

	branchTeamMemberRole, err := entity.NewBranchTeamMemberRole(input)
	require.NoError(t, err)

	// When timestamps are nil, they should be set to current time
	assert.False(t, branchTeamMemberRole.CreatedAt().IsZero())
	assert.False(t, branchTeamMemberRole.UpdatedAt().IsZero())

	// They should be very close to current time (within 1 second)
	now := time.Now().UTC()
	assert.WithinDuration(t, now, branchTeamMemberRole.CreatedAt(), time.Second)
	assert.WithinDuration(t, now, branchTeamMemberRole.UpdatedAt(), time.Second)
}
