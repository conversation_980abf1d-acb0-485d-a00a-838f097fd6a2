package branch_area

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/event"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/service"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
)

type Factory struct {
	BranchAreaValidationService           *service.BranchAreaValidationService
	BranchTeamMemberRoleValidationService *service.BranchTeamMemberRoleValidationService
	BranchAreaRepo                        repository.BranchAreaRepo
	ProcessBranchAreaEventUsecase         *usecase.ProcessBranchAreaEventUsecase
	BranchAreaEventListener               *event.BranchAreaEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	processBranchAreaEventUsecase *usecase.ProcessBranchAreaEventUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.ProcessBranchAreaEventInput) error {
	// Direct delegation to usecase - no conversion needed since types are the same
	return a.processBranchAreaEventUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	branchAreaValidationService := service.NewBranchAreaValidationService()
	branchTeamMemberRoleValidationService := service.NewBranchTeamMemberRoleValidationService()

	// Initialize repository
	BranchAreaRepo := repository.NewBranchAreaRepo(db)

	// Initialize usecase
	processBranchAreaEventUsecase := usecase.NewProcessBranchAreaEventUsecase(
		BranchAreaRepo,
		branchAreaValidationService,
		branchTeamMemberRoleValidationService,
	)

	adapter := &usecaseAdapter{processBranchAreaEventUsecase: processBranchAreaEventUsecase}

	// Initialize event listener
	eventListener, err := event.NewBranchAreaEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		BranchAreaValidationService:           &branchAreaValidationService,
		BranchTeamMemberRoleValidationService: &branchTeamMemberRoleValidationService,
		BranchAreaRepo:                        BranchAreaRepo,
		ProcessBranchAreaEventUsecase:         processBranchAreaEventUsecase,
		BranchAreaEventListener:               eventListener,
	}, nil
}
