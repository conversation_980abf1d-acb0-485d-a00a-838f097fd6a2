package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestBranchAreaRepo_Creation(t *testing.T) {
	t.Run("Creates repository successfully", func(t *testing.T) {
		repo := repository.NewBranchAreaRepo(nil)
		assert.NotNil(t, repo)
	})
}

func TestBranchAreaRepository_Interface(t *testing.T) {
	t.Run("Implements BranchAreaRepo interface", func(t *testing.T) {
		var _ repository.BranchAreaRepo = repository.NewBranchAreaRepo(nil)
	})
}

func TestNewBranchAreaRepo(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewBranchAreaRepo(db)

	assert.NotNil(t, repo)
}

func TestBranchAreaRepo_UpsertBranchArea(t *testing.T) {
	validBranchID := core.NewID()
	validAreaID := core.NewID()
	now := time.Now().UTC()

	// Create a test branch area entity
	branchArea, err := entity.NewBranchArea(&entity.NewBranchAreaInput{
		BranchID:  validBranchID.Value(),
		AreaID:    validAreaID.Value(),
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name           string
		branchArea     *entity.BranchArea
		setupMock      func(sqlmock.Sqlmock)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name:       "Successful upsert - INSERT",
			branchArea: branchArea,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.branch_area WHERE branch_id`).
					WithArgs(validBranchID.Value(), validAreaID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.branch_area.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validBranchID.Value(), validAreaID.Value(), now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:       "Database error",
			branchArea: branchArea,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.branch_area WHERE branch_id`).
					WithArgs(validBranchID.Value(), validAreaID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.branch_area.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validBranchID.Value(), validAreaID.Value(), now, now).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewBranchAreaRepo(db)
			err := repo.UpsertBranchArea(context.Background(), tt.branchArea)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestBranchAreaRepo_UpsertBranchTeamMemberRole(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()
	now := time.Now().UTC()

	branchTeamMemberRole, err := entity.NewBranchTeamMemberRole(&entity.NewBranchTeamMemberRoleInput{
		BranchID:  validBranchID.Value(),
		AccountID: validAccountID.Value(),
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	entityID := branchTeamMemberRole.ID()

	tests := []struct {
		name                 string
		branchTeamMemberRole *entity.BranchTeamMemberRole
		setupMock            func(sqlmock.Sqlmock)
		wantErr              bool
		wantErrMessage       string
	}{
		{
			name:                 "Successful upsert - INSERT",
			branchTeamMemberRole: branchTeamMemberRole,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.branch_team_member_role WHERE branch_id`).
					WithArgs(validBranchID.Value(), validAccountID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.branch_team_member_role.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(entityID, validBranchID.Value(), validAccountID.Value(), now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:                 "Database error",
			branchTeamMemberRole: branchTeamMemberRole,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.branch_team_member_role WHERE branch_id`).
					WithArgs(validBranchID.Value(), validAccountID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.branch_team_member_role.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(entityID, validBranchID.Value(), validAccountID.Value(), now, now).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewBranchAreaRepo(db)
			err := repo.UpsertBranchTeamMemberRole(context.Background(), tt.branchTeamMemberRole)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
