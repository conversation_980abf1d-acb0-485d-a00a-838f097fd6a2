package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// BranchAreaRepo defines the interface for account branch area upsert operations
type BranchAreaRepo interface {
	UpsertBranchArea(ctx context.Context, branchArea *entity.BranchArea) error
	UpsertBranchTeamMemberRole(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error
}

// BranchAreaRepo implements BranchAreaRepo
type branchAreaRepo struct {
	db *sqlx.DB
}

// NewBranchAreaRepo creates a new account branch area repository
func NewBranchAreaRepo(db *sqlx.DB) BranchAreaRepo {
	return &branchAreaRepo{db: db}
}

// BranchAreaMappingExistsError represents an error when a branch area mapping already exists
type BranchAreaMappingExistsError struct {
	BranchID string
	AreaID   string
}

func (e *BranchAreaMappingExistsError) Error() string {
	return fmt.Sprintf("branch area mapping for branch '%s' and area '%s' already exists", e.BranchID, e.AreaID)
}

// BranchTeamMemberRoleMappingExistsError represents an error when a branch team member role mapping already exists
type BranchTeamMemberRoleMappingExistsError struct {
	BranchID  string
	AccountID string
}

func (e *BranchTeamMemberRoleMappingExistsError) Error() string {
	return fmt.Sprintf("branch team member role mapping for branch '%s' and account '%s' already exists", e.BranchID, e.AccountID)
}

// UpsertBranchArea upserts a branch area entity into the database
func (r *branchAreaRepo) UpsertBranchArea(ctx context.Context, branchArea *entity.BranchArea) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "BranchAreaRepository"), vlog.F("method", "UpsertBranchArea"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.branch_area WHERE branch_id = $1 AND area_id = $2`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("branch_id", branchArea.BranchID()), vlog.F("area_id", branchArea.AreaID()))
	err := r.db.QueryRowContext(ctx, checkQuery, branchArea.BranchID(), branchArea.AreaID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing branch area", vlog.F("error", err))
		return fmt.Errorf("failed to check existing branch area: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", branchArea.UpdatedAt()),
			vlog.F("incoming_is_newer", branchArea.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && branchArea.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("branch area event discarded - older than existing record",
			vlog.F("branch_id", branchArea.BranchID()),
			vlog.F("area_id", branchArea.AreaID()),
			vlog.F("event_updated_at", branchArea.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.branch_area (branch_id, area_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (branch_id, area_id) DO UPDATE SET
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.branch_area.updated_at`

	logger.Debug("executing upsert query",
		vlog.F("query", upsertQuery),
		vlog.F("branch_id", branchArea.BranchID()),
		vlog.F("area_id", branchArea.AreaID()),
		vlog.F("created_at", branchArea.CreatedAt()),
		vlog.F("updated_at", branchArea.UpdatedAt()))

	_, err = r.db.ExecContext(ctx, upsertQuery,
		branchArea.BranchID(),
		branchArea.AreaID(),
		branchArea.CreatedAt(),
		branchArea.UpdatedAt())

	if err != nil {
		logger.Error("failed to upsert branch area", vlog.F("error", err))
		return fmt.Errorf("failed to upsert branch area: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("branch area updated successfully",
			vlog.F("branch_id", branchArea.BranchID()),
			vlog.F("area_id", branchArea.AreaID()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("branch area inserted successfully",
			vlog.F("branch_id", branchArea.BranchID()),
			vlog.F("area_id", branchArea.AreaID()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}

// UpsertBranchTeamMemberRole upserts a branch team member role entity into the database
func (r *branchAreaRepo) UpsertBranchTeamMemberRole(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "BranchAreaRepository"), vlog.F("method", "UpsertBranchTeamMemberRole"))

	// First, try to get the existing record to check timestamp
	var existingUpdatedAt *time.Time
	checkQuery := `SELECT updated_at FROM public.branch_team_member_role WHERE branch_id = $1 AND account_id = $2`
	err := r.db.GetContext(ctx, &existingUpdatedAt, checkQuery, branchTeamMemberRole.BranchID(), branchTeamMemberRole.AccountID())
	if err != nil && err != sql.ErrNoRows {
		logger.Error("failed to check existing branch team member role", vlog.F("error", err))
		return core.NewBusinessError("database error occurred while checking existing branch team member role: %v", err)
	}

	// If record exists and has newer timestamp, skip update
	if existingUpdatedAt != nil && existingUpdatedAt.After(branchTeamMemberRole.UpdatedAt()) {
		logger.Debug("skipping branch team member role update - existing record is newer",
			vlog.F("branch_id", branchTeamMemberRole.BranchID()),
			vlog.F("account_id", branchTeamMemberRole.AccountID()),
			vlog.F("existing_updated_at", existingUpdatedAt),
			vlog.F("new_updated_at", branchTeamMemberRole.UpdatedAt()))
		return nil
	}

	// Perform upsert
	upsertQuery := `
		INSERT INTO public.branch_team_member_role (id, branch_id, account_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (branch_id, account_id)
		DO UPDATE SET
			updated_at = EXCLUDED.updated_at
		WHERE branch_team_member_role.updated_at <= EXCLUDED.updated_at`

	_, err = r.db.ExecContext(ctx, upsertQuery,
		branchTeamMemberRole.ID(),
		branchTeamMemberRole.BranchID(),
		branchTeamMemberRole.AccountID(),
		branchTeamMemberRole.CreatedAt(),
		branchTeamMemberRole.UpdatedAt())

	if err != nil {
		// Check for constraint violations that indicate business errors
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			mappingExistsErr := &BranchTeamMemberRoleMappingExistsError{
				BranchID:  branchTeamMemberRole.BranchID(),
				AccountID: branchTeamMemberRole.AccountID(),
			}
			if errors.As(err, &mappingExistsErr) {
				logger.Error("Branch team member role mapping constraint violation - message will go to DLQ",
					vlog.F("error", err),
					vlog.F("branch_id", branchTeamMemberRole.BranchID()),
					vlog.F("account_id", branchTeamMemberRole.AccountID()))
				return core.NewBusinessError("Branch team member role mapping '%s-%s' already exists", mappingExistsErr.BranchID, mappingExistsErr.AccountID)
			}
		}

		logger.Error("failed to upsert branch team member role", vlog.F("error", err))
		return core.NewBusinessError("database error occurred while upserting branch team member role: %v", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("branch team member role updated successfully",
			vlog.F("branch_id", branchTeamMemberRole.BranchID()),
			vlog.F("account_id", branchTeamMemberRole.AccountID()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("branch team member role inserted successfully",
			vlog.F("branch_id", branchTeamMemberRole.BranchID()),
			vlog.F("account_id", branchTeamMemberRole.AccountID()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
