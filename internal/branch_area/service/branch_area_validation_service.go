package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
)

// BranchAreaValidationService provides validation operations for branch area mapping domain
type BranchAreaValidationService interface {
	ValidateBranchAreaBusinessRules(ctx context.Context, branchArea *entity.BranchArea) error
	ValidateBranchAreaUpsertInput(ctx context.Context, input *BranchAreaUpsertValidationInput) error
}

// BranchAreaUpsertValidationInput contains data for branch area upsert validation
type BranchAreaUpsertValidationInput struct {
	BranchID string
	AreaID   string
}

type branchAreaValidationService struct{}

// NewBranchAreaValidationService creates a new branch area validation service
func NewBranchAreaValidationService() BranchAreaValidationService {
	return &branchAreaValidationService{}
}

// BranchTeamMemberRoleValidationService defines the interface for branch team member role validation
type BranchTeamMemberRoleValidationService interface {
	ValidateBranchTeamMemberRoleUpsertInput(ctx context.Context, input *BranchTeamMemberRoleUpsertValidationInput) error
	ValidateBranchTeamMemberRoleBusinessRules(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error
}

// BranchTeamMemberRoleUpsertValidationInput contains data for validating branch team member role upsert operations
type BranchTeamMemberRoleUpsertValidationInput struct {
	BranchID  string `validate:"required"`
	AccountID string `validate:"required"`
}

type branchTeamMemberRoleValidationService struct{}

func NewBranchTeamMemberRoleValidationService() BranchTeamMemberRoleValidationService {
	return &branchTeamMemberRoleValidationService{}
}

func (s *branchTeamMemberRoleValidationService) ValidateBranchTeamMemberRoleUpsertInput(ctx context.Context, input *BranchTeamMemberRoleUpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}
	if input.AccountID == "" {
		return core.NewBusinessError("account ID is required")
	}

	// Validate ID formats
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.AccountID); err != nil {
		return core.NewBusinessError("invalid account ID format: %v", err)
	}

	return nil
}

func (s *branchTeamMemberRoleValidationService) ValidateBranchTeamMemberRoleBusinessRules(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error {
	if branchTeamMemberRole == nil {
		return core.NewBusinessError("branch team member role is required")
	}

	return nil
}

// ValidateBranchAreaBusinessRules validates business-specific rules for branch area entity
func (s *branchAreaValidationService) ValidateBranchAreaBusinessRules(ctx context.Context, branchArea *entity.BranchArea) error {
	if branchArea == nil {
		return core.NewBusinessError("branch area entity is required")
	}

	if branchArea.BranchID() == "" {
		return core.NewBusinessError("branch ID is required")
	}

	if branchArea.AreaID() == "" {
		return core.NewBusinessError("area ID is required")
	}

	return nil
}

// ValidateBranchAreaUpsertInput validates input for branch area upsert operations
func (s *branchAreaValidationService) ValidateBranchAreaUpsertInput(ctx context.Context, input *BranchAreaUpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("branch area upsert input is required")
	}

	// Validate branch ID is not empty
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}

	// Validate area ID is not empty
	if input.AreaID == "" {
		return core.NewBusinessError("area ID is required")
	}

	// Validate ID formats using core validation
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}

	if _, err := core.NewIDFromString(input.AreaID); err != nil {
		return core.NewBusinessError("invalid area ID format: %v", err)
	}

	return nil
}
