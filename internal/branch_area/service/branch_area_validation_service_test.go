package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/service"
)

func TestBranchAreaValidationService_ValidateBranchAreaUpsertInput(t *testing.T) {
	validationService := service.NewBranchAreaValidationService()
	ctx := context.Background()
	validBranchID := core.NewID().Value()
	validAreaID := core.NewID().Value()

	tests := []struct {
		name    string
		input   *service.BranchAreaUpsertValidationInput
		wantErr bool
		errMsg  string
	}{
		{
			name: "Valid input",
			input: &service.BranchAreaUpsertValidationInput{
				BranchID: validBranchID,
				AreaID:   validAreaID,
			},
			wantErr: false,
		},
		{
			name: "Empty branch ID",
			input: &service.BranchAreaUpsertValidationInput{
				BranchID: "",
				AreaID:   validAreaID,
			},
			wantErr: true,
			errMsg:  "branch ID is required",
		},
		{
			name: "Empty area ID",
			input: &service.BranchAreaUpsertValidationInput{
				BranchID: validBranchID,
				AreaID:   "",
			},
			wantErr: true,
			errMsg:  "area ID is required",
		},
		{
			name: "Invalid branch ID format",
			input: &service.BranchAreaUpsertValidationInput{
				BranchID: "invalid-uuid-format",
				AreaID:   validAreaID,
			},
			wantErr: true,
			errMsg:  "invalid branch ID format",
		},
		{
			name: "Invalid area ID format",
			input: &service.BranchAreaUpsertValidationInput{
				BranchID: validBranchID,
				AreaID:   "invalid-uuid-format",
			},
			wantErr: true,
			errMsg:  "invalid area ID format",
		},
		{
			name:    "Nil input",
			input:   nil,
			wantErr: true,
			errMsg:  "input is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateBranchAreaUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBranchAreaValidationService_ValidateBranchAreaBusinessRules(t *testing.T) {
	validationService := service.NewBranchAreaValidationService()
	ctx := context.Background()
	validBranchID := core.NewID().Value()
	validAreaID := core.NewID().Value()

	tests := []struct {
		name        string
		branchArea  *entity.BranchArea
		wantErr     bool
		errMsg      string
		setupEntity func() *entity.BranchArea
	}{
		{
			name: "Valid branch area",
			setupEntity: func() *entity.BranchArea {
				branchArea, _ := entity.NewBranchArea(&entity.NewBranchAreaInput{
					BranchID: validBranchID,
					AreaID:   validAreaID,
				})
				return branchArea
			},
			wantErr: false,
		},
		{
			name:        "Nil branch area",
			setupEntity: func() *entity.BranchArea { return nil },
			wantErr:     true,
			errMsg:      "branch area entity is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			branchArea := tt.setupEntity()
			err := validationService.ValidateBranchAreaBusinessRules(ctx, branchArea)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNewBranchAreaValidationService(t *testing.T) {
	service := service.NewBranchAreaValidationService()
	assert.NotNil(t, service)
}

func TestBranchTeamMemberRoleValidationService_ValidateBranchTeamMemberRoleUpsertInput(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()

	tests := []struct {
		name           string
		input          *service.BranchTeamMemberRoleUpsertValidationInput
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Valid input",
			input: &service.BranchTeamMemberRoleUpsertValidationInput{
				BranchID:  validBranchID.Value(),
				AccountID: validAccountID.Value(),
			},
			wantErr: false,
		},
		{
			name: "Empty branch ID",
			input: &service.BranchTeamMemberRoleUpsertValidationInput{
				BranchID:  "",
				AccountID: validAccountID.Value(),
			},
			wantErr:        true,
			wantErrMessage: "branch ID is required",
		},
		{
			name: "Empty account ID",
			input: &service.BranchTeamMemberRoleUpsertValidationInput{
				BranchID:  validBranchID.Value(),
				AccountID: "",
			},
			wantErr:        true,
			wantErrMessage: "account ID is required",
		},
		{
			name: "Invalid branch ID format",
			input: &service.BranchTeamMemberRoleUpsertValidationInput{
				BranchID:  "invalid-uuid",
				AccountID: validAccountID.Value(),
			},
			wantErr:        true,
			wantErrMessage: "invalid branch ID format",
		},
		{
			name: "Invalid account ID format",
			input: &service.BranchTeamMemberRoleUpsertValidationInput{
				BranchID:  validBranchID.Value(),
				AccountID: "invalid-uuid",
			},
			wantErr:        true,
			wantErrMessage: "invalid account ID format",
		},
		{
			name:           "Nil input",
			input:          nil,
			wantErr:        true,
			wantErrMessage: "validation input is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := service.NewBranchTeamMemberRoleValidationService()
			ctx := context.Background()

			err := service.ValidateBranchTeamMemberRoleUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBranchTeamMemberRoleValidationService_ValidateBranchTeamMemberRoleBusinessRules(t *testing.T) {
	validBranchID := core.NewID()
	validAccountID := core.NewID()
	now := time.Now().UTC()

	// Create a valid branch team member role entity
	branchTeamMemberRole, err := entity.NewBranchTeamMemberRole(&entity.NewBranchTeamMemberRoleInput{
		BranchID:  validBranchID.Value(),
		AccountID: validAccountID.Value(),
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name                 string
		branchTeamMemberRole *entity.BranchTeamMemberRole
		wantErr              bool
		wantErrMessage       string
	}{
		{
			name:                 "Valid branch team member role",
			branchTeamMemberRole: branchTeamMemberRole,
			wantErr:              false,
		},
		{
			name:                 "Nil branch team member role",
			branchTeamMemberRole: nil,
			wantErr:              true,
			wantErrMessage:       "branch team member role is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := service.NewBranchTeamMemberRoleValidationService()
			ctx := context.Background()

			err := service.ValidateBranchTeamMemberRoleBusinessRules(ctx, tt.branchTeamMemberRole)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNewBranchTeamMemberRoleValidationService(t *testing.T) {
	service := service.NewBranchTeamMemberRoleValidationService()
	assert.NotNil(t, service)
}
