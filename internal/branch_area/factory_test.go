package branch_area_test

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	sqlxDB := sqlx.NewDb(db, "sqlmock")
	return sqlxDB, mock
}

func TestNewFactory(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLBranchArea: "https://sqs.us-east-1.amazonaws.com/*********/account-branch-area-queue",
			Region:                "us-east-1",
			AccessKeyID:           "test-access-key",
			SecretAccessKey:       "test-secret-key",
		},
	}

	factory, err := branch_area.NewFactory(db, cfg)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.BranchAreaEventListener)
}

func TestNewFactory_EmptyConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			// Empty AWS config
		},
	}

	factory, err := branch_area.NewFactory(db, cfg)

	// Should fail gracefully with empty config
	// The factory should handle empty config gracefully
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, factory)
	} else {
		assert.NotNil(t, factory)
	}
}

func TestNewFactory_InvalidAWSConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLBranchArea: "invalid-url",
			Region:                "",
			AccessKeyID:           "",
			SecretAccessKey:       "",
		},
	}

	// This should fail due to invalid AWS configuration
	factory, err := branch_area.NewFactory(db, cfg)

	// Should fail with invalid config
	assert.Error(t, err)
	assert.Nil(t, factory)
}

func TestFactory_Structure(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory structure test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLBranchArea: "https://sqs.us-east-1.amazonaws.com/*********/account-branch-area-queue",
			Region:                "us-east-1",
			AccessKeyID:           "test-access-key",
			SecretAccessKey:       "test-secret-key",
		},
	}

	factory, err := branch_area.NewFactory(db, cfg)
	assert.NoError(t, err)

	// Verify factory structure
	assert.NotNil(t, factory)

	// Test that all required services are available
	assert.NotNil(t, factory.BranchAreaEventListener)
}

func TestUsecaseAdapter_Execute(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping usecase adapter test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLBranchArea: "https://sqs.us-east-1.amazonaws.com/*********/account-branch-area-queue",
			Region:                "us-east-1",
			AccessKeyID:           "test-access-key",
			SecretAccessKey:       "test-secret-key",
		},
	}

	factory, err := branch_area.NewFactory(db, cfg)
	assert.NoError(t, err)
	assert.NotNil(t, factory)

	// Test that the adapter is properly wired within the event listener
	// The actual functionality is tested in the event processor tests
	assert.NotNil(t, factory.BranchAreaEventListener)
}

func TestNewFactory_NilDatabase(t *testing.T) {
	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLBranchArea: "https://sqs.us-east-1.amazonaws.com/*********/account-branch-area-queue",
			Region:                "us-east-1",
			AccessKeyID:           "test-access-key",
			SecretAccessKey:       "test-secret-key",
		},
	}

	// This should fail or panic due to nil database
	// The factory should handle nil database gracefully or fail early
	defer func() {
		if r := recover(); r != nil {
			// Expected panic due to nil database
			assert.Contains(t, r.(string), "nil")
		}
	}()

	factory, err := branch_area.NewFactory(nil, cfg)

	// Either error or panic is acceptable
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, factory)
	}
}
