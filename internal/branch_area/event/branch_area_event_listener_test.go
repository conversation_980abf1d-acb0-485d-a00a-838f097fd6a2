package event_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/event"
	usecase_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/branch_area/usecase"
)

func TestNewBranchAreaEventListener(t *testing.T) {
	tests := []struct {
		name           string
		cfg            config.Configuration
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Missing queue URL",
			cfg: config.Configuration{
				AWS: config.AWSConfig{
					SQSQueueURLBranchArea: "",
				},
			},
			wantErr:        true,
			wantErrMessage: "AWS_SQS_QUEUE_URL_BRANCH_AREA is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUsecase := new(usecase_mocks.MockProcessBranchAreaEventUsecase)

			listener, err := event.NewBranchAreaEventListener(tt.cfg, mockUsecase)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, listener)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, listener)
			}
		})
	}
}
