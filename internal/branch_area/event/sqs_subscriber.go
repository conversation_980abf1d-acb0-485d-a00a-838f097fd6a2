package event

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSBranchAreaEventSubscriber implements BranchAreaEventSubscriber using AWS SQS
type SQSBranchAreaEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes an account branch area event message
		ProcessMessage(ctx context.Context, message *BranchAreaEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSBranchAreaEventSubscriber creates a new SQS-based account branch area event subscriber
func NewSQSBranchAreaEventSubscriber(cfg config.Configuration, queueURL string) (*SQSBranchAreaEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize account branch area SQS client: %w", err)
	}

	return &SQSBranchAreaEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("subscriber", "BranchAreaEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe configures the event processor for the subscriber
func (s *SQSBranchAreaEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *BranchAreaEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start starts the subscription service
func (s *SQSBranchAreaEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("account branch area event subscriber is already running")
	}

	if s.processor == nil {
		return fmt.Errorf("event processor is required")
	}

	s.logger.Info("starting account branch area event subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx)

	return nil
}

// Stop stops the subscription service
func (s *SQSBranchAreaEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping account branch area event subscriber")

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	if s.sqsClient != nil {
		s.sqsClient.Close()
	}

	s.logger.Info("account branch area event subscriber stopped successfully")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSBranchAreaEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSBranchAreaEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("account branch area event subscriber polling stopped")
			return
		case <-ctx.Done():
			s.logger.Info("account branch area event subscriber context cancelled")
			return
		case <-ticker.C:
			s.processMessages(ctx)
		}
	}
}

// processMessages processes messages from SQS
func (s *SQSBranchAreaEventSubscriber) processMessages(ctx context.Context) {
	if s.processor == nil {
		s.logger.Warn("no processor set for account branch area event subscriber")
		return
	}

	result, err := s.sqsClient.ReceiveMessages(ctx, 10, 20)
	if err != nil {
		s.logger.Error("failed to receive messages from account branch area event SQS", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	for _, message := range result.Messages {
		processErr := s.processMessage(ctx, message)

		if processErr != nil {
			// Check if this is a business error
			var businessErr *core.BusinessError
			if errors.As(processErr, &businessErr) {
				s.logger.Error("business error occurred - deleting message to prevent retries",
					vlog.F("error", processErr),
					vlog.F("messageId", *message.MessageId),
					vlog.F("error_type", "BUSINESS_ERROR"),
					vlog.F("action", "DELETE_MESSAGE"))

				// Delete the message to prevent retries
				if message.ReceiptHandle != nil {
					if _, deleteErr := s.sqsClient.DeleteMessage(ctx, *message.ReceiptHandle); deleteErr != nil {
						s.logger.Error("failed to delete business error message from account branch area event SQS",
							vlog.F("error", deleteErr),
							vlog.F("messageId", *message.MessageId),
							vlog.F("original_error", processErr))
					} else {
						s.logger.Info("business error message deleted - will not be retried",
							vlog.F("messageId", *message.MessageId),
							vlog.F("original_error", processErr))
					}
				}
			} else {
				// This is a transient error - let SQS retry
				s.logger.Error("transient error occurred - message will be retried by account branch area event SQS",
					vlog.F("error", processErr),
					vlog.F("messageId", *message.MessageId),
					vlog.F("error_type", "TRANSIENT_ERROR"),
					vlog.F("action", "RETRY"))
			}
		} else {
			// Success - delete the message
			if message.ReceiptHandle != nil {
				if _, deleteErr := s.sqsClient.DeleteMessage(ctx, *message.ReceiptHandle); deleteErr != nil {
					s.logger.Error("failed to delete processed message from account branch area event SQS",
						vlog.F("error", deleteErr),
						vlog.F("messageId", *message.MessageId))
				} else {
					s.logger.Debug("successfully processed and deleted account branch area message",
						vlog.F("messageId", *message.MessageId))
				}
			}
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSBranchAreaEventSubscriber) processMessage(ctx context.Context, sqsMessage types.Message) error {
	s.logger.Debug("processing account branch area message", vlog.F("messageId", *sqsMessage.MessageId))

	var eventMessage BranchAreaEventMessage
	if err := json.Unmarshal([]byte(*sqsMessage.Body), &eventMessage); err != nil {
		s.logger.Error("failed to unmarshal account branch area message", vlog.F("error", err), vlog.F("messageId", *sqsMessage.MessageId))
		return fmt.Errorf("failed to unmarshal account branch area message: %w", err)
	}

	if err := s.processor.ProcessMessage(ctx, &eventMessage); err != nil {
		return fmt.Errorf("failed to handle account branch area event message: %w", err)
	}

	return nil
}
