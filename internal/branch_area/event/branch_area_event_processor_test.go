package event

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
)

// MockProcessBranchAreaEventUsecase is a mock implementation of ProcessBranchAreaEventUsecase
type MockProcessBranchAreaEventUsecase struct {
	mock.Mock
}

func (m *MockProcessBranchAreaEventUsecase) Execute(ctx context.Context, input *usecase.ProcessBranchAreaEventInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func TestNewBranchAreaEventProcessor(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
	assert.Equal(t, mockUsecase, processor.processUsecase)
}

func TestBranchAreaEventProcessor_ProcessMessage_Success(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	testTime, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

	message := &BranchAreaEventMessage{
		ID:                      "550e8400-e29b-41d4-a716-************",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               testTime,
	}

	expectedInput := &usecase.ProcessBranchAreaEventInput{
		BranchID:                "550e8400-e29b-41d4-a716-************",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               &testTime,
	}

	mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil)

	err := processor.ProcessMessage(context.Background(), message)

	assert.NoError(t, err)
	mockUsecase.AssertExpectations(t)
}

func TestBranchAreaEventProcessor_ProcessMessage_NilMessage(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	err := processor.ProcessMessage(context.Background(), nil)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "message is required")
}

func TestBranchAreaEventProcessor_ProcessMessage_EmptyBranchID(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	testTime, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

	message := &BranchAreaEventMessage{
		ID:                      "",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               testTime,
	}

	err := processor.ProcessMessage(context.Background(), message)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "branch ID is required")
}

func TestBranchAreaEventProcessor_ProcessMessage_EmptyAreas(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	testTime, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

	message := &BranchAreaEventMessage{
		ID:                      "550e8400-e29b-41d4-a716-************",
		Areas:                   []string{},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               testTime,
	}

	err := processor.ProcessMessage(context.Background(), message)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "areas are required")
}

func TestBranchAreaEventProcessor_ProcessMessage_InvalidBranchIDFormat(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	testTime, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

	message := &BranchAreaEventMessage{
		ID:                      "invalid-uuid",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               testTime,
	}

	err := processor.ProcessMessage(context.Background(), message)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid branch ID format")
}

func TestBranchAreaEventProcessor_ProcessMessage_UsecaseError(t *testing.T) {
	mockUsecase := &MockProcessBranchAreaEventUsecase{}
	processor := NewBranchAreaEventProcessor(mockUsecase)

	testTime, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

	message := &BranchAreaEventMessage{
		ID:                      "550e8400-e29b-41d4-a716-************",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               testTime,
	}

	expectedInput := &usecase.ProcessBranchAreaEventInput{
		BranchID:                "550e8400-e29b-41d4-a716-************",
		Areas:                   []string{"84ce4d70-a442-412b-bf27-06f4544a8661"},
		RepresentativeAccountID: "660e8400-e29b-41d4-a716-************",
		UpdatedAt:               &testTime,
	}

	usecaseError := core.NewBusinessError("usecase failed")
	mockUsecase.On("Execute", mock.Anything, expectedInput).Return(usecaseError)

	err := processor.ProcessMessage(context.Background(), message)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "usecase failed")
	mockUsecase.AssertExpectations(t)
}
