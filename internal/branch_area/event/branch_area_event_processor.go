package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// BranchAreaEventProcessor processes account branch area events
type BranchAreaEventProcessor struct {
	processUsecase ProcessBranchAreaEventUsecase
}

// NewBranchAreaEventProcessor creates a new account branch area event processor
func NewBranchAreaEventProcessor(processUsecase ProcessBranchAreaEventUsecase) *BranchAreaEventProcessor {
	return &BranchAreaEventProcessor{
		processUsecase: processUsecase,
	}
}

// ProcessMessage processes an account branch area event message
func (p *BranchAreaEventProcessor) ProcessMessage(ctx context.Context, message *BranchAreaEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "BranchAreaEventProcessor"))
	logger.Debug("handling account branch area event message", vlog.F("branch_id", message.ID))

	// Validate message data
	if message.ID == "" {
		logger.Error("validation failed", vlog.F("error", "branch ID is required"))
		return core.NewBusinessError("branch ID is required in message data")
	}

	if len(message.Areas) == 0 {
		logger.Error("validation failed", vlog.F("error", "areas are required"))
		return core.NewBusinessError("areas are required in message data")
	}

	if message.RepresentativeAccountID == "" {
		logger.Error("validation failed", vlog.F("error", "representative account ID is required"))
		return core.NewBusinessError("representative account ID is required in message data")
	}

	// Validate ID formats
	if _, err := core.NewIDFromString(message.ID); err != nil {
		logger.Error("validation failed", vlog.F("error", "invalid branch ID format"), vlog.F("branch_id", message.ID))
		return core.NewBusinessError("invalid branch ID format in message data: %v", err)
	}

	if _, err := core.NewIDFromString(message.RepresentativeAccountID); err != nil {
		logger.Error("validation failed", vlog.F("error", "invalid representative account ID format"), vlog.F("account_id", message.RepresentativeAccountID))
		return core.NewBusinessError("invalid representative account ID format in message data: %v", err)
	}

	// Validate area IDs
	for _, areaID := range message.Areas {
		if _, err := core.NewIDFromString(areaID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid area ID format"), vlog.F("area_id", areaID))
			return core.NewBusinessError("invalid area ID format: %v", err)
		}
	}

	// Convert to usecase input
	processInput := &usecase.ProcessBranchAreaEventInput{
		BranchID:                message.ID,
		Areas:                   message.Areas,
		RepresentativeAccountID: message.RepresentativeAccountID,
		UpdatedAt:               &message.UpdatedAt,
	}

	// Execute the process operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	err := p.processUsecase.Execute(ctxWithLogger, processInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("branch_id", message.ID),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			return core.NewBusinessError("Business error processing account branch area for branch '%s': %v", message.ID, err)
		}

		// This is a transient error - should be retried
		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("branch_id", message.ID),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing account branch area event: %w", err)
	}

	logger.Info("successfully processed account branch area event",
		vlog.F("branch_id", message.ID),
		vlog.F("areas_count", len(message.Areas)))

	return nil
}

// IsBusinessError determines if an error should be treated as a business error
func (p *BranchAreaEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"branch area mapping",
		"branch team member role",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"branch_area_branch_id_area_id_key",
		"branch_team_member_role_branch_id_account_id_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	var businessErr *core.BusinessError
	return errors.As(err, &businessErr)
}
