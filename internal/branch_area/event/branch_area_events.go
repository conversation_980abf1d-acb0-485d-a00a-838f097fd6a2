package event

import (
	"time"
)

// BranchAreaEventMessage represents the event message structure for account branch area mapping operations
type BranchAreaEventMessage struct {
	ID                      string    `json:"id"`                        // branch ID
	Areas                   []string  `json:"areas"`                     // array of area IDs
	RepresentativeAccountID string    `json:"representative_account_id"` // account ID for the representative
	UpdatedAt               time.Time `json:"updated_at"`                // timestamp
}
