package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// ProcessBranchAreaEventUsecase defines the interface for processing account branch area events
type ProcessBranchAreaEventUsecase interface {
	Execute(ctx context.Context, input *usecase.ProcessBranchAreaEventInput) error
}

// BranchAreaEventSubscriber defines the interface for subscribing to account branch area events
type BranchAreaEventSubscriber interface {
	// Subscribe starts listening to account branch area events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *BranchAreaEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// BranchAreaEventListener manages the account branch area event subscription service
type BranchAreaEventListener struct {
	subscriber BranchAreaEventSubscriber
	cfg        config.Configuration
}

// NewBranchAreaEventListener creates a new account branch area event listener instance
func NewBranchAreaEventListener(cfg config.Configuration, processBranchAreaEventUsecase ProcessBranchAreaEventUsecase) (*BranchAreaEventListener, error) {
	// Validate that account branch area queue URL is configured
	if cfg.AWS.SQSQueueURLBranchArea == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_BRANCH_AREA is required for account branch area event operations")
	}

	// Initialize SQS event subscriber with account branch area queue URL
	subscriber, err := NewSQSBranchAreaEventSubscriber(cfg, cfg.AWS.SQSQueueURLBranchArea)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event handler
	eventProcessor := NewBranchAreaEventProcessor(processBranchAreaEventUsecase)

	// Set up subscriber with handler
	if err := subscriber.Subscribe(context.Background(), eventProcessor); err != nil {
		return nil, fmt.Errorf("failed to set up event subscription: %w", err)
	}

	return &BranchAreaEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the account branch area event listener
func (s *BranchAreaEventListener) Start(ctx context.Context) error {
	logger := vlog.New().With(vlog.F("service", "BranchAreaEventListener"))
	logger.Info("starting account branch area event listener")

	if err := s.subscriber.Start(ctx); err != nil {
		logger.Error("failed to start event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start event subscriber: %w", err)
	}

	logger.Info("account branch area event listener started successfully")
	return nil
}

// Stop stops the account branch area event listener
func (s *BranchAreaEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("service", "BranchAreaEventListener"))
	logger.Info("stopping account branch area event listener")

	if err := s.subscriber.Stop(); err != nil {
		logger.Error("failed to stop event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop event subscriber: %w", err)
	}

	logger.Info("account branch area event listener stopped successfully")
	return nil
}

// IsRunning returns true if the service is currently running
func (s *BranchAreaEventListener) IsRunning() bool {
	return s.subscriber.IsRunning()
}
