package usecase

import (
	"context"
	"errors"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var processBranchAreaEventUsecaseName = "ProcessBranchAreaEventUsecase"

type ProcessBranchAreaEventInput struct {
	BranchID                string
	Areas                   []string
	RepresentativeAccountID string
	UpdatedAt               *time.Time
}

type ProcessBranchAreaEventUsecase struct {
	BranchAreaRepo                        repository.BranchAreaRepo
	branchAreaValidationService           service.BranchAreaValidationService
	branchTeamMemberRoleValidationService service.BranchTeamMemberRoleValidationService
}

func NewProcessBranchAreaEventUsecase(
	BranchAreaRepo repository.BranchAreaRepo,
	branchAreaValidationService service.BranchAreaValidationService,
	branchTeamMemberRoleValidationService service.BranchTeamMemberRoleValidationService,
) *ProcessBranchAreaEventUsecase {
	return &ProcessBranchAreaEventUsecase{
		BranchAreaRepo:                        BranchAreaRepo,
		branchAreaValidationService:           branchAreaValidationService,
		branchTeamMemberRoleValidationService: branchTeamMemberRoleValidationService,
	}
}

func (uc *ProcessBranchAreaEventUsecase) Execute(ctx context.Context, input *ProcessBranchAreaEventInput) error {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", processBranchAreaEventUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate input
	if err := uc.validateInput(input); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return err
	}

	for _, areaID := range input.Areas {
		logger.Debug("processing branch area mapping", vlog.F("branch_id", input.BranchID), vlog.F("area_id", areaID))

		// Validate
		serviceInput := &service.BranchAreaUpsertValidationInput{
			BranchID: input.BranchID,
			AreaID:   areaID,
		}
		if err := uc.branchAreaValidationService.ValidateBranchAreaUpsertInput(ctx, serviceInput); err != nil {
			logger.Error("branch area service validation failed", vlog.F("error", err), vlog.F("area_id", areaID))
			return err
		}

		// Create branch area entity
		entityInput := &entity.NewBranchAreaInput{
			BranchID:  input.BranchID,
			AreaID:    areaID,
			UpdatedAt: input.UpdatedAt,
		}

		branchArea, err := entity.NewBranchArea(entityInput)
		if err != nil {
			logger.Error("failed to create branch area entity", vlog.F("error", err), vlog.F("area_id", areaID))
			return core.NewBusinessError("failed to create branch area entity: %v", err)
		}

		// Validate
		if err := uc.branchAreaValidationService.ValidateBranchAreaBusinessRules(ctx, branchArea); err != nil {
			logger.Error("branch area business rules validation failed", vlog.F("error", err), vlog.F("area_id", areaID))
			return err
		}

		// Upsert branch area entity
		if err := uc.BranchAreaRepo.UpsertBranchArea(ctx, branchArea); err != nil {
			// Check if this is a mapping constraint violation
			var mappingExistsErr *repository.BranchAreaMappingExistsError
			if errors.As(err, &mappingExistsErr) {
				logger.Error("Branch area mapping constraint violation - message will go to DLQ",
					vlog.F("error", err),
					vlog.F("branch_id", branchArea.BranchID()),
					vlog.F("area_id", branchArea.AreaID()))
				return core.NewBusinessError("Branch area mapping '%s-%s' already exists", mappingExistsErr.BranchID, mappingExistsErr.AreaID)
			}

			logger.Error("failed to upsert branch area", vlog.F("error", err), vlog.F("area_id", areaID))
			return core.NewBusinessError("database error occurred while upserting branch area: %v", err)
		}

		logger.Debug("branch area processed successfully",
			vlog.F("branch_id", branchArea.BranchID()),
			vlog.F("area_id", branchArea.AreaID()))
	}

	logger.Debug("execution finished",
		vlog.F("branch_id", input.BranchID),
		vlog.F("areas_count", len(input.Areas)),
		vlog.F("representative_account_id", input.RepresentativeAccountID))
	return nil
}

// validateInput validates the input using core validation patterns
func (uc *ProcessBranchAreaEventUsecase) validateInput(input *ProcessBranchAreaEventInput) error {
	if input == nil {
		return core.NewBusinessError("input is required")
	}

	// Use core validation patterns
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}
	if len(input.Areas) == 0 {
		return core.NewBusinessError("at least one area is required")
	}
	if input.RepresentativeAccountID == "" {
		return core.NewBusinessError("representative account ID is required")
	}

	// Validate ID formats using core
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.RepresentativeAccountID); err != nil {
		return core.NewBusinessError("invalid representative account ID format: %v", err)
	}

	// Validate each area ID format
	for i, areaID := range input.Areas {
		if areaID == "" {
			return core.NewBusinessError("area ID at index %d is empty", i)
		}
		if _, err := core.NewIDFromString(areaID); err != nil {
			return core.NewBusinessError("invalid area ID format at index %d: %v", i, err)
		}
	}

	return nil
}
