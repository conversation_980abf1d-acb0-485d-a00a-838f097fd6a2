package usecase_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
	repository_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/branch_area/repository"
	service_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/branch_area/service"
)

func TestProcessBranchAreaEventUsecase_Execute(t *testing.T) {
	validBranchID := core.NewID()
	validAreaID1 := core.NewID()
	validAreaID2 := core.NewID()
	validAccountID := core.NewID()
	validUpdatedAt := time.Now()

	tests := []struct {
		name           string
		input          *usecase.ProcessBranchAreaEventInput
		setupMocks     func(*repository_mocks.MockBranchAreaRepo, *service_mocks.MockBranchAreaValidationService, *service_mocks.MockBranchTeamMemberRoleValidationService)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with multiple areas",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                validBranchID.Value(),
				Areas:                   []string{validAreaID1.Value(), validAreaID2.Value()},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				branchAreaValidation.On("ValidateBranchAreaUpsertInput", mock.Anything, mock.AnythingOfType("*service.BranchAreaUpsertValidationInput")).Return(nil).Times(2)
				branchAreaValidation.On("ValidateBranchAreaBusinessRules", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).Return(nil).Times(2)
				BranchAreaRepo.On("UpsertBranchArea", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).Return(nil).Times(2)
			},
			wantErr: false,
		},
		{
			name: "Successful processing with single area",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                validBranchID.Value(),
				Areas:                   []string{validAreaID1.Value()},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				branchAreaValidation.On("ValidateBranchAreaUpsertInput", mock.Anything, mock.AnythingOfType("*service.BranchAreaUpsertValidationInput")).Return(nil)
				branchAreaValidation.On("ValidateBranchAreaBusinessRules", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).Return(nil)
				BranchAreaRepo.On("UpsertBranchArea", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:  "Validation error - nil input",
			input: nil,
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				// No mocks needed
			},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
		{
			name: "Validation error - empty branch ID",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                "",
				Areas:                   []string{validAreaID1.Value()},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				// No mocks needed
			},
			wantErr:        true,
			wantErrMessage: "branch ID is required",
		},
		{
			name: "Validation error - empty areas",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                validBranchID.Value(),
				Areas:                   []string{},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				// No mocks needed
			},
			wantErr:        true,
			wantErrMessage: "at least one area is required",
		},
		{
			name: "Validation error - invalid branch ID format",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                "invalid-id",
				Areas:                   []string{validAreaID1.Value()},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				// No mocks needed
			},
			wantErr:        true,
			wantErrMessage: "invalid branch ID format",
		},
		{
			name: "Validation error - invalid area ID format",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                validBranchID.Value(),
				Areas:                   []string{"invalid-area-id"},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				// No mocks needed
			},
			wantErr:        true,
			wantErrMessage: "invalid area ID format",
		},
		{
			name: "Repository error - branch area upsert fails",
			input: &usecase.ProcessBranchAreaEventInput{
				BranchID:                validBranchID.Value(),
				Areas:                   []string{validAreaID1.Value()},
				RepresentativeAccountID: validAccountID.Value(),
				UpdatedAt:               &validUpdatedAt,
			},
			setupMocks: func(BranchAreaRepo *repository_mocks.MockBranchAreaRepo, branchAreaValidation *service_mocks.MockBranchAreaValidationService, teamMemberValidation *service_mocks.MockBranchTeamMemberRoleValidationService) {
				branchAreaValidation.On("ValidateBranchAreaUpsertInput", mock.Anything, mock.AnythingOfType("*service.BranchAreaUpsertValidationInput")).Return(nil)
				branchAreaValidation.On("ValidateBranchAreaBusinessRules", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).Return(nil)
				BranchAreaRepo.On("UpsertBranchArea", mock.Anything, mock.AnythingOfType("*entity.BranchArea")).
					Return(errors.New("database connection failed"))
			},
			wantErr:        true,
			wantErrMessage: "database error occurred while upserting branch area",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockBranchAreaRepo := new(repository_mocks.MockBranchAreaRepo)
			mockBranchAreaValidation := new(service_mocks.MockBranchAreaValidationService)
			mockTeamMemberValidation := new(service_mocks.MockBranchTeamMemberRoleValidationService)

			// Setup mocks
			tt.setupMocks(mockBranchAreaRepo, mockBranchAreaValidation, mockTeamMemberValidation)

			// Create usecase
			uc := usecase.NewProcessBranchAreaEventUsecase(
				mockBranchAreaRepo,
				mockBranchAreaValidation,
				mockTeamMemberValidation,
			)

			// Execute
			ctx := context.Background()
			err := uc.Execute(ctx, tt.input)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all expectations were met
			mockBranchAreaRepo.AssertExpectations(t)
			mockBranchAreaValidation.AssertExpectations(t)
			mockTeamMemberValidation.AssertExpectations(t)
		})
	}
}
