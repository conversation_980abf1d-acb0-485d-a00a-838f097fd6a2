package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// NatureEventSubscriber defines the interface for subscribing to nature events
type NatureEventSubscriber interface {
	// Subscribe starts listening to nature events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *NatureEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// NatureEventListener manages the nature event subscription service
type NatureEventListener struct {
	subscriber NatureEventSubscriber
	cfg        config.Configuration
}

// NewNatureEventListener creates a new nature event listener instance
func NewNatureEventListener(cfg config.Configuration, upsertUsecase UpsertNatureUsecase) (*NatureEventListener, error) {
	// Validate that nature queue URL is configured
	if cfg.AWS.SQSQueueURLNature == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_NATURE is required for nature event operations")
	}

	// Initialize SQS event subscriber with nature queue URL
	subscriber, err := NewSQSNatureEventSubscriber(cfg, cfg.AWS.SQSQueueURLNature)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event handler
	eventProcessor := NewNatureUpsertEventProcessor(upsertUsecase)

	// Set up subscriber with handler
	if err := subscriber.Subscribe(context.Background(), eventProcessor); err != nil {
		return nil, fmt.Errorf("failed to set up event subscription: %w", err)
	}

	return &NatureEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the nature event listener
func (s *NatureEventListener) Start(ctx context.Context) error {
	logger := vlog.New().With(vlog.F("service", "NatureEventListener"))
	logger.Info("starting nature event listener")

	if err := s.subscriber.Start(ctx); err != nil {
		logger.Error("failed to start event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start event subscriber: %w", err)
	}

	logger.Info("nature event listener started successfully")
	return nil
}

// Stop stops the nature event listener
func (s *NatureEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("service", "NatureEventListener"))
	logger.Info("stopping nature event listener")

	if err := s.subscriber.Stop(); err != nil {
		logger.Error("failed to stop event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop event subscriber: %w", err)
	}

	logger.Info("nature event listener stopped successfully")
	return nil
}

// IsRunning returns true if the service is currently running
func (s *NatureEventListener) IsRunning() bool {
	return s.subscriber.IsRunning()
}
