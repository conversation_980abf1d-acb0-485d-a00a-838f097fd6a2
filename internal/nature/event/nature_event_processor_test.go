package event_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/event"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
	usecase_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/nature/usecase"
)

func TestNatureUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()
	testTime := time.Now()
	tests := []struct {
		name           string
		message        *event.NatureEventMessage
		setupMock      func(*usecase_mocks.MockUpsertNatureUsecase)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with ID",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
				UpdatedAt:   testTime,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				expectedInput := &usecase.UpsertNatureInput{
					ID:          core.ToPtr(validID.Value()),
					Name:        "Cardiology",
					Description: "Heart-related medical procedures",
					Icon:        "heart-icon.png",
					Enabled:     true,
					UpdatedAt:   &testTime,
				}
				expectedOutput := &usecase.UpsertNatureOutput{
					ID:          validID.Value(),
					Name:        "Cardiology",
					Description: "Heart-related medical procedures",
					Icon:        "heart-icon.png",
					Enabled:     true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing without ID",
			message: &event.NatureEventMessage{
				ID:          nil,
				Name:        "Dermatology",
				Description: "Skin-related medical procedures",
				Icon:        "skin-icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				expectedInput := &usecase.UpsertNatureInput{
					ID:          nil,
					Name:        "Dermatology",
					Description: "Skin-related medical procedures",
					Icon:        "skin-icon.png",
					Enabled:     true,
					UpdatedAt:   &time.Time{},
				}
				newID := core.NewID()
				expectedOutput := &usecase.UpsertNatureOutput{
					ID:          newID.Value(),
					Name:        "Dermatology",
					Description: "Skin-related medical procedures",
					Icon:        "skin-icon.png",
					Enabled:     true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Empty name validation error",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "",
				Description: "Description",
				Icon:        "icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				// Processor should not call usecase due to validation error
			},
			wantErr:        true,
			wantErrMessage: "nature name is required",
		},
		{
			name: "Empty description validation error",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Valid Name",
				Description: "",
				Icon:        "icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				// Processor should not call usecase due to validation error
			},
			wantErr:        true,
			wantErrMessage: "nature description is required",
		},
		{
			name: "Empty icon validation error",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Valid Name",
				Description: "Valid Description",
				Icon:        "",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				// Processor should not call usecase due to validation error
			},
			wantErr:        true,
			wantErrMessage: "nature icon is required",
		},
		{
			name: "Invalid ID format validation error",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr("invalid-id-format"),
				Name:        "Valid Name",
				Description: "Valid Description",
				Icon:        "valid-icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				// Processor should not call usecase due to validation error
			},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Usecase execution error",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.AnythingOfType("*usecase.UpsertNatureInput")).
					Return(nil, errors.New("database connection failed"))
			},
			wantErr:        true,
			wantErrMessage: "transient error processing nature upsert",
		},
		{
			name: "Business error from usecase",
			message: &event.NatureEventMessage{
				Name:        "duplicate-name",
				Description: "Duplicate nature name",
				Icon:        "duplicate-icon.png",
				Enabled:     true,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				expectedInput := &usecase.UpsertNatureInput{
					ID:          nil,
					Name:        "duplicate-name",
					Description: "Duplicate nature name",
					Icon:        "duplicate-icon.png",
					Enabled:     true,
					UpdatedAt:   &time.Time{},
				}
				businessErr := core.NewBusinessError("Nature name 'duplicate-name' already exists")
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, businessErr)
			},
			wantErr:        true,
			wantErrMessage: "Business error processing nature 'duplicate-name'",
		},
		{
			name: "Disabled nature processing",
			message: &event.NatureEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Disabled Nature",
				Description: "This nature is disabled",
				Icon:        "disabled-icon.png",
				Enabled:     false,
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertNatureUsecase) {
				expectedInput := &usecase.UpsertNatureInput{
					ID:          core.ToPtr(validID.Value()),
					Name:        "Disabled Nature",
					Description: "This nature is disabled",
					Icon:        "disabled-icon.png",
					Enabled:     false,
					UpdatedAt:   &time.Time{},
				}
				expectedOutput := &usecase.UpsertNatureOutput{
					ID:          validID.Value(),
					Name:        "Disabled Nature",
					Description: "This nature is disabled",
					Icon:        "disabled-icon.png",
					Enabled:     false,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock
			mockUsecase := new(usecase_mocks.MockUpsertNatureUsecase)
			tt.setupMock(mockUsecase)

			// Create processor
			processor := event.NewNatureUpsertEventProcessor(mockUsecase)

			// Execute
			ctx := context.Background()
			err := processor.ProcessMessage(ctx, tt.message)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify expectations
			mockUsecase.AssertExpectations(t)
		})
	}
}

func TestNatureUpsertEventProcessor_ProcessMessage_NilMessage(t *testing.T) {
	mockUsecase := new(usecase_mocks.MockUpsertNatureUsecase)
	processor := event.NewNatureUpsertEventProcessor(mockUsecase)

	ctx := context.Background()
	err := processor.ProcessMessage(ctx, nil)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "message is required")

	// Verify no usecase calls were made
	mockUsecase.AssertExpectations(t)
}

func TestNatureUpsertEventProcessor_Interface(t *testing.T) {
	mockUsecase := new(usecase_mocks.MockUpsertNatureUsecase)
	processor := event.NewNatureUpsertEventProcessor(mockUsecase)

	// Test that processor implements the interface
	// Interface is now defined in transport layer where it's consumed
	var _ interface {
		ProcessMessage(ctx context.Context, message *event.NatureEventMessage) error
	} = processor
}

func TestNewNatureUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(usecase_mocks.MockUpsertNatureUsecase)
	processor := event.NewNatureUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
}

func TestNatureEventMessage_Structure(t *testing.T) {
	validID := core.NewID()

	message := &event.NatureEventMessage{
		ID:          core.ToPtr(validID.Value()),
		Name:        "Test Nature",
		Description: "Test description",
		Icon:        "test-icon.png",
		Enabled:     true,
	}

	// Verify structure
	assert.Equal(t, validID.Value(), *message.ID)
	assert.Equal(t, "Test Nature", message.Name)
	assert.Equal(t, "Test description", message.Description)
	assert.Equal(t, "test-icon.png", message.Icon)
	assert.Equal(t, true, message.Enabled)
}

func TestNatureUpsertEventProcessor_IsBusinessError(t *testing.T) {
	processor := event.NewNatureUpsertEventProcessor(nil)

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "constraint violation error",
			err:      errors.New("duplicate key value violates unique constraint \"nature_name_key\""),
			expected: true,
		},
		{
			name:     "nature name error",
			err:      errors.New("nature name 'Test' already exists"),
			expected: true,
		},
		{
			name:     "core business error",
			err:      core.NewBusinessError("business logic error"),
			expected: true,
		},
		{
			name:     "regular error",
			err:      errors.New("database connection failed"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.IsBusinessError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}
