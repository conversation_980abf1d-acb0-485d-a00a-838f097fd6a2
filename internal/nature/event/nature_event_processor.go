package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertNatureUsecase defines the interface for nature upsert operations
type UpsertNatureUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertNatureInput) (*usecase.UpsertNatureOutput, error)
}

// NatureUpsertEventProcessor processes nature upsert events
type NatureUpsertEventProcessor struct {
	upsertUsecase UpsertNatureUsecase
}

// NewNatureUpsertEventProcessor creates a new nature upsert event processor
func NewNatureUpsertEventProcessor(upsertUsecase UpsertNatureUsecase) *NatureUpsertEventProcessor {
	return &NatureUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// IsBusinessError determines if an error is a business logic error that should go to DLQ
func (p *NatureUpsertEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"nature name",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"nature_name_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	// Check for core business errors
	var businessErr *core.BusinessError
	if errors.As(err, &businessErr) {
		return true
	}

	return false
}

// ProcessMessage processes a nature event message
func (p *NatureUpsertEventProcessor) ProcessMessage(ctx context.Context, message *NatureEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "NatureUpsertEventProcessor"))
	logger.Debug("handling nature event message")

	// Validate message data using core functions
	if message.Name == "" {
		logger.Error("validation failed", vlog.F("error", "nature name is required"))
		return core.NewBusinessError("nature name is required in message data")
	}
	if message.Description == "" {
		logger.Error("validation failed", vlog.F("error", "nature description is required"))
		return core.NewBusinessError("nature description is required in message data")
	}
	if message.Icon == "" {
		logger.Error("validation failed", vlog.F("error", "nature icon is required"))
		return core.NewBusinessError("nature icon is required in message data")
	}

	// Validate ID format if provided
	if message.ID != nil && *message.ID != "" {
		if _, err := core.NewIDFromString(*message.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertNatureInput{
		Name:        message.Name,
		Description: message.Description,
		Icon:        message.Icon,
		Enabled:     message.Enabled,
		UpdatedAt:   &message.UpdatedAt,
	}

	if message.ID != nil {
		upsertInput.ID = message.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("nature", message.Name),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			// Return a business error that SQS will recognize as non-retryable
			return core.NewBusinessError("Business error processing nature '%s': %v", message.Name, err)
		}

		// This is a transient error - should be retried
		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("nature", message.Name),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing nature upsert: %w", err)
	}

	logger.Info("successfully processed nature upsert", vlog.F("name", message.Name))
	return nil
}
