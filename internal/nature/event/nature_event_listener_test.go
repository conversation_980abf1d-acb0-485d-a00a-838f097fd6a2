package event_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/event"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
)

// MockNatureEventSubscriber is a mock implementation of NatureEventSubscriber
type MockNatureEventSubscriber struct {
	mock.Mock
}

func (m *MockNatureEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *event.NatureEventMessage) error
}) error {
	args := m.Called(ctx, processor)
	return args.Error(0)
}

func (m *MockNatureEventSubscriber) Start(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockNatureEventSubscriber) Stop() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockNatureEventSubscriber) IsRunning() bool {
	args := m.Called()
	return args.Bool(0)
}

// MockUpsertNatureUsecaseForListener is a mock implementation of UpsertNatureUsecase
type MockUpsertNatureUsecaseForListener struct {
	mock.Mock
}

func (m *MockUpsertNatureUsecaseForListener) Execute(ctx context.Context, input *usecase.UpsertNatureInput) (*usecase.UpsertNatureOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertNatureOutput), args.Error(1)
}

func TestNewNatureEventListener_MissingQueueURL(t *testing.T) {
	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLNature: "", // Missing queue URL
			Region:            "us-east-1",
			AccessKeyID:       "test-access-key",
			SecretAccessKey:   "test-secret-key",
		},
	}

	mockUsecase := new(MockUpsertNatureUsecaseForListener)

	listener, err := event.NewNatureEventListener(cfg, mockUsecase)

	assert.Error(t, err)
	assert.Nil(t, listener)
	assert.Contains(t, err.Error(), "AWS_SQS_QUEUE_URL_NATURE is required")
}

func TestNewNatureEventListener_ValidConfig(t *testing.T) {
	// Skip this test as it requires real AWS credentials for SQS initialization
	t.Skip("Skipping event listener test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLNature: "https://sqs.us-east-1.amazonaws.com/123456789/nature-queue",
			Region:            "us-east-1",
			AccessKeyID:       "test-access-key",
			SecretAccessKey:   "test-secret-key",
		},
	}

	mockUsecase := new(MockUpsertNatureUsecaseForListener)

	listener, err := event.NewNatureEventListener(cfg, mockUsecase)

	assert.NoError(t, err)
	assert.NotNil(t, listener)
}
