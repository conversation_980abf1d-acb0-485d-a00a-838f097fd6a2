package usecase

import (
	"context"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var upsertNatureUsecaseName = "UpsertNatureUsecase"

type NatureRepository interface {
	UpsertNature(ctx context.Context, nature *entity.Nature) error
}

type UpsertNatureInput struct {
	ID          *string
	Name        string
	Description string
	Icon        string
	Enabled     bool
	UpdatedAt   *time.Time
}

type UpsertNatureOutput struct {
	ID          string
	Name        string
	Description string
	Icon        string
	Enabled     bool
}

type UpsertNatureUsecase struct {
	repo              NatureRepository
	validationService service.NatureValidationService
}

func NewUpsertNatureUsecase(repo NatureRepository, validationService service.NatureValidationService) *UpsertNatureUsecase {
	return &UpsertNatureUsecase{
		repo:              repo,
		validationService: validationService,
	}
}

func (uc *UpsertNatureUsecase) Execute(ctx context.Context, input *UpsertNatureInput) (*UpsertNatureOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", upsertNatureUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate input
	if err := uc.validateInput(input); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return nil, err
	}

	// Validate using service layer business rules
	serviceInput := &service.UpsertValidationInput{
		ID:          input.ID,
		Name:        input.Name,
		Description: input.Description,
		Icon:        input.Icon,
		Enabled:     input.Enabled,
	}
	if err := uc.validationService.ValidateUpsertInput(ctx, serviceInput); err != nil {
		logger.Error("service validation failed", vlog.F("error", err))
		return nil, err
	}

	// Determine entity ID
	entityID := core.NewID().Value()
	if input.ID != nil && *input.ID != "" {
		entityID = *input.ID
	}

	// Create nature entity
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          entityID,
		Name:        input.Name,
		Description: input.Description,
		Icon:        input.Icon,
		Enabled:     input.Enabled,
		UpdatedAt:   input.UpdatedAt,
	})
	if err != nil {
		logger.Error("failed to create nature entity", vlog.F("error", err))
		return nil, core.NewBusinessError("failed to create nature entity: %v", err)
	}

	// Validate business rules using service layer
	if err := uc.validationService.ValidateBusinessRules(ctx, nature); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return nil, err
	}

	// Upsert nature entity
	if err := uc.repo.UpsertNature(ctx, nature); err != nil {
		logger.Error("failed to upsert nature", vlog.F("error", err))
		return nil, core.NewBusinessError("database error occurred while upserting nature: %v", err)
	}

	// Create output
	out := &UpsertNatureOutput{
		ID:          nature.ID(),
		Name:        nature.Name(),
		Description: nature.Description(),
		Icon:        nature.Icon(),
		Enabled:     nature.Enabled(),
	}

	logger.Debug("execution finished", vlog.F("output", out))
	return out, nil
}

// validateInput validates the input using core validation patterns
func (uc *UpsertNatureUsecase) validateInput(input *UpsertNatureInput) error {
	if input == nil {
		return core.NewBusinessError("input is required")
	}

	// Use core validation patterns
	if input.Name == "" {
		return core.NewBusinessError("name is required")
	}
	if input.Description == "" {
		return core.NewBusinessError("description is required")
	}
	if input.Icon == "" {
		return core.NewBusinessError("icon is required")
	}

	// Validate UUID format if provided using core
	if input.ID != nil && *input.ID != "" {
		if _, err := core.NewIDFromString(*input.ID); err != nil {
			return core.NewBusinessError("invalid UUID format: %v", err)
		}
	}

	return nil
}
