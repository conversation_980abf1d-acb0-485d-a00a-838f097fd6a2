package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// NatureUpsertRepository defines the interface for nature upsert operations
type NatureUpsertRepository interface {
	UpsertNature(ctx context.Context, nature *entity.Nature) error
}

// natureUpsertRepository implements NatureUpsertRepository
type natureUpsertRepository struct {
	db *sqlx.DB
}

// NewNatureUpsertRepository creates a new nature upsert repository
func NewNatureUpsertRepository(db *sqlx.DB) *natureUpsertRepository {
	return &natureUpsertRepository{
		db: db,
	}
}

// UpsertNature upserts a nature entity into the database
func (r *natureUpsertRepository) UpsertNature(ctx context.Context, nature *entity.Nature) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "NatureRepository"), vlog.F("method", "UpsertNature"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.nature WHERE uuid = $1`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("uuid", nature.ID()))
	err := r.db.QueryRowContext(ctx, checkQuery, nature.ID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing nature", vlog.F("error", err))
		return fmt.Errorf("failed to check existing nature: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", nature.UpdatedAt()),
			vlog.F("incoming_is_newer", nature.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && nature.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("nature event discarded - older than existing record",
			vlog.F("id", nature.ID()),
			vlog.F("name", nature.Name()),
			vlog.F("event_updated_at", nature.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.nature (uuid, name, description, icon, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		ON CONFLICT (uuid) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.nature.updated_at
	`

	args := []interface{}{
		nature.ID(),
		nature.Name(),
		nature.Description(),
		nature.Icon(),
		nature.Enabled(),
		nature.CreatedAt(),
		nature.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)
	if err != nil {
		// Check for name constraint violation
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr), vlog.F("nature", nature.Name()))
			return parsedErr
		}

		logger.Error("failed to upsert nature", vlog.F("error", err))
		return fmt.Errorf("failed to upsert nature: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("nature updated successfully",
			vlog.F("id", nature.ID()),
			vlog.F("name", nature.Name()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("nature inserted successfully",
			vlog.F("id", nature.ID()),
			vlog.F("name", nature.Name()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
