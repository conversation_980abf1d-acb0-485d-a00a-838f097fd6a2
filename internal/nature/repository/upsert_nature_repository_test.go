package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewNatureUpsertRepository(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureUpsertRepository(db)

	assert.NotNil(t, repo)
}

func TestNatureUpsertRepository_UpsertNature(t *testing.T) {
	validID := core.NewID()
	now := time.Now().UTC()

	// Create a test nature entity
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "cardiology",
		Description: "Heart-related medical procedures",
		Icon:        "heart-icon.png",
		Enabled:     true,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name           string
		nature         *entity.Nature
		setupMock      func(sqlmock.Sqlmock)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name:   "Successful upsert - INSERT",
			nature: nature,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.nature WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				// Expect successful INSERT
				expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "cardiology", "Heart-related medical procedures", "heart-icon.png", true, now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:   "Database error",
			nature: nature,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.nature WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "cardiology", "Heart-related medical procedures", "heart-icon.png", true, now, now).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name:   "Name constraint violation",
			nature: nature,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.nature WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "nature_name_key"`)
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "cardiology", "Heart-related medical procedures", "heart-icon.png", true, now, now).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "nature_name_key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewNatureUpsertRepository(db)
			err := repo.UpsertNature(context.Background(), tt.nature)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestNatureUpsertRepository_UpsertNature_DisabledNature(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a disabled nature entity
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "retired",
		Description: "Retired nature",
		Icon:        "retired-icon.png",
		Enabled:     false,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	mock.ExpectQuery(`SELECT updated_at FROM public\.nature WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnError(sql.ErrNoRows)

	expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "retired", "Retired nature", "retired-icon.png", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewNatureUpsertRepository(db)
	err = repo.UpsertNature(context.Background(), nature)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureUpsertRepository_UpsertNature_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a nature entity for update
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "updated-cardiology",
		Description: "Updated description",
		Icon:        "updated-icon.png",
		Enabled:     false,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	olderTime := now.Add(-1 * time.Hour)
	mock.ExpectQuery(`SELECT updated_at FROM public\.nature WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&olderTime))

	expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "updated-cardiology", "Updated description", "updated-icon.png", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewNatureUpsertRepository(db)
	err = repo.UpsertNature(context.Background(), nature)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
