package nature

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/event"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/service"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
)

type Factory struct {
	NatureEventListener *event.NatureEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertNatureUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertNatureInput) (*usecase.UpsertNatureOutput, error) {
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewNatureValidationService()

	// Initialize repository for upsert operations
	natureUpsertRepo := repository.NewNatureUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertNatureUsecase(natureUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewNatureEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		NatureEventListener: eventListener,
	}, nil
}
