package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
)

// AreaValidationService provides validation operations for area domain
type AreaValidationService interface {
	ValidateLabelAndUUID(ctx context.Context, label string, uuid string) error
	ValidateBusinessRules(ctx context.Context, area *entity.Area) error
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error
}

// UpsertValidationInput contains data for upsert validation
type UpsertValidationInput struct {
	ID      *string
	Label   string
	Acronym string
	Enabled bool
}

type validationService struct{}

// NewAreaValidationService creates a new validation service
func NewAreaValidationService() AreaValidationService {
	return &validationService{}
}

// ValidateLabelAndUUID validates label and uuid combination for business rules
func (s *validationService) ValidateLabelAndUUID(ctx context.Context, label string, uuid string) error {
	// Validate label is not empty
	if label == "" {
		return core.NewBusinessError("area label is required")
	}

	// Validate uuid format if provided using core validation
	if uuid != "" {
		if _, err := core.NewIDFromString(uuid); err != nil {
			return core.NewBusinessError("invalid uuid format: %v", err)
		}
	}

	return nil
}

// ValidateBusinessRules validates business-specific rules for area entity
func (s *validationService) ValidateBusinessRules(ctx context.Context, area *entity.Area) error {
	if area == nil {
		return core.NewBusinessError("area entity is required")
	}

	if area.Label() == "" {
		return core.NewBusinessError("area label is required")
	}

	if area.Acronym() == "" {
		return core.NewBusinessError("area acronym is required")
	}

	return nil
}

// ValidateUpsertInput validates input for upsert operations
func (s *validationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("upsert input is required")
	}

	uuid := ""
	if input.ID != nil {
		uuid = *input.ID
	}

	if err := s.ValidateLabelAndUUID(ctx, input.Label, uuid); err != nil {
		return err
	}

	if input.Acronym == "" {
		return core.NewBusinessError("area acronym is required")
	}

	return nil
}
