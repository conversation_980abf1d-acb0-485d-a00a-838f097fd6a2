package area

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/area/event"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/service"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	db                *sqlx.DB
	logger            vlog.Logger
	AreaEventListener *event.AreaEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertAreaUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertAreaInput) (*usecase.UpsertAreaOutput, error) {
	// Direct delegation to usecase - no conversion needed since types are the same
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration, logger vlog.Logger) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewAreaValidationService()

	// Initialize repository for upsert operations
	areaUpsertRepo := repository.NewAreaUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertAreaUsecase(areaUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Only initialize event listener if SQS URL is set
	var eventListener *event.AreaEventListener
	if cfg.AWS.SQSQueueURLArea != "" {
		var err error
		eventListener, err = event.NewAreaEventListener(cfg, adapter)
		if err != nil {
			return nil, err
		}
	}

	return &Factory{
		db:                db,
		logger:            logger,
		AreaEventListener: eventListener,
	}, nil
}

func (f *Factory) getRepoOrPanic() *repository.AreaRepository {
	if f == nil || f.db == nil || f.logger == nil {
		panic("Factory, db, or logger is nil")
	}
	return repository.NewAreaRepository(f.db)
}

func (f *Factory) ListAreaHandler() *transport.ListAreasHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewListAreaUsecase(repo)
	h := transport.NewListAreasHandler(uc)
	return h
}

func (f *Factory) GetAreaHandler() *transport.GetAreaHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewGetAreaUsecase(repo)
	h := transport.NewGetAreaHandler(uc)
	return h
}
