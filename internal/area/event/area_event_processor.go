package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAreaUsecase defines the interface for area upsert operations
type UpsertAreaUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertAreaInput) (*usecase.UpsertAreaOutput, error)
}

// AreaUpsertEventProcessor processes area upsert events
type AreaUpsertEventProcessor struct {
	upsertUsecase UpsertAreaUsecase
}

// NewAreaUpsertEventProcessor creates a new area upsert event processor
func NewAreaUpsertEventProcessor(upsertUsecase UpsertAreaUsecase) *AreaUpsertEventProcessor {
	return &AreaUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// IsBusinessError determines if an error is a business logic error that should go to DLQ
func (p *AreaUpsertEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"area acronym",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"area_acronym_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	// Check for core business errors
	var businessErr *core.BusinessError
	if errors.As(err, &businessErr) {
		return true
	}

	return false
}

// ProcessMessage processes an area event message
func (p *AreaUpsertEventProcessor) ProcessMessage(ctx context.Context, message *AreaEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "AreaUpsertEventProcessor"))
	logger.Debug("handling area event message")

	// Validate message data
	if message.Label == "" {
		logger.Error("validation failed", vlog.F("error", "area label is required"))
		return core.NewBusinessError("area label is required in message data")
	}
	if message.Acronym == "" {
		logger.Error("validation failed", vlog.F("error", "area acronym is required"))
		return core.NewBusinessError("area acronym is required in message data")
	}

	// Validate ID format if provided
	if message.ID != nil && *message.ID != "" {
		if _, err := core.NewIDFromString(*message.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertAreaInput{
		Label:     message.Label,
		Acronym:   message.Acronym,
		Enabled:   message.Enabled,
		UpdatedAt: &message.UpdatedAt,
	}

	if message.ID != nil {
		upsertInput.ID = message.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("area", message.Acronym),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			// Return a business error that SQS will recognize as non-retryable
			return core.NewBusinessError("Business error processing area '%s': %v", message.Acronym, err)
		}

		// This is a transient error - should be retried
		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("area", message.Acronym),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing area upsert: %w", err)
	}

	logger.Info("successfully processed area upsert",
		vlog.F("label", message.Label))
	return nil
}
