package transport

import (
	"context"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

type listAreaUsecase interface {
	Execute(ctx context.Context, params repository.ListAreaFilter) (*usecase.ListAreaOutput, int64, error)
}

type ListAreasHandler struct {
	usecase listAreaUsecase
}

type ListAreaModel struct {
	ID        string `json:"id"`
	Label     string `json:"label"`
	Enabled   bool   `json:"enabled"`
	Acronym   string `json:"acronym"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

type ListAreasResponse struct {
	Status     bool                   `json:"status"`
	Message    string                 `json:"message"`
	Data       interface{}            `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

func NewListAreasHandler(uc listAreaUsecase) *ListAreasHandler {
	return &ListAreasHandler{usecase: uc}
}

func (h *ListAreasHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		page, err := strconv.Atoi(c.QueryParam("page"))
		if err != nil || page < 0 {
			page = 0
		}
		size, err := strconv.Atoi(c.QueryParam("size"))
		if err != nil || size <= 0 {
			size = 10
		}
		if size > 100 {
			size = 100
		}
		orderBy := c.QueryParam("orderBy")
		direction := c.QueryParam("direction")
		acronym := c.QueryParam("acronym")
		label := c.QueryParam("label")
		enabledStr := c.QueryParam("enabled")
		var enabled *bool
		if enabledStr != "" {
			val, err := strconv.ParseBool(enabledStr)
			if err != nil {
				resp := ListAreasResponse{
					Status:     false,
					Message:    "Invalid query parameters provided",
					Data:       []interface{}{},
					Pagination: nil,
				}
				return c.JSON(http.StatusBadRequest, resp)
			}
			enabled = &val
		}

		params := repository.ListAreaFilter{
			Page:      page,
			Size:      size,
			OrderBy:   orderBy,
			Direction: direction,
			Acronym:   &acronym,
			Label:     &label,
			Enabled:   enabled,
		}

		output, total, err := h.usecase.Execute(c.Request().Context(), params)
		if err != nil {
			resp := ListAreasResponse{
				Status:     false,
				Message:    "Internal server error",
				Data:       []interface{}{},
				Pagination: nil,
			}
			return c.JSON(http.StatusInternalServerError, resp)
		}

		pagination := map[string]interface{}{
			"current_page": page,
			"page_size":    size,
			"total":        total,
			"total_pages":  (total + int64(size) - 1) / int64(size),
		}

		var data []*ListAreaModel
		if output != nil && output.Data != nil {
			for _, item := range output.Data {
				data = append(data, &ListAreaModel{
					ID:        item.ID,
					Label:     item.Label,
					Enabled:   item.Enabled,
					Acronym:   item.Acronym,
					CreatedAt: item.CreatedAt,
					UpdatedAt: item.UpdatedAt,
				})
			}
		}
		if data == nil {
			data = []*ListAreaModel{}
		}

		resp := ListAreasResponse{
			Status:     true,
			Message:    "Areas retrieved successfully.",
			Data:       data,
			Pagination: pagination,
		}
		return c.JSON(http.StatusOK, resp)
	}
}
