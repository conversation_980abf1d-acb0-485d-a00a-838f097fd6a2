package transport

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

type getAreaUsecase interface {
	Execute(ctx context.Context, areaID string) (*usecase.GetAreaOutput, error)
}

type GetAreaHandler struct {
	usecase getAreaUsecase
}

type GetAreaResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type GetAreaModel struct {
	ID        string `json:"id"`
	Label     string `json:"label"`
	Enabled   bool   `json:"enabled"`
	Acronym   string `json:"acronym"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

func NewGetAreaHandler(uc getAreaUsecase) *GetAreaHandler {
	return &GetAreaHandler{usecase: uc}
}

func (h *GetAreaHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		areaID := c.Param("id")
		if areaID == "" {
			return c.JSON(http.StatusBadRequest, GetAreaResponse{
				Status:  false,
				Message: "Invalid area ID format",
				Data:    []interface{}{},
			})
		}

		output, err := h.usecase.Execute(c.Request().Context(), areaID)
		if err != nil {
			fmt.Printf("DEBUG: get_area_handler.go not found error: %v\n", err)
			if strings.Contains(err.Error(), "invalid input syntax for type uuid") {
				return c.JSON(http.StatusBadRequest, GetAreaResponse{
					Status:  false,
					Message: "Invalid area ID format",
					Data:    []interface{}{},
				})
			}
			if errors.Is(err, sql.ErrNoRows) ||
				err.Error() == "area not found" ||
				err.Error() == "Area with the specified ID does not exist" ||
				strings.Contains(err.Error(), "area not found") ||
				strings.Contains(err.Error(), "no rows in result set") ||
				strings.Contains(err.Error(), "Area with the specified ID does not exist") {
				return c.JSON(http.StatusNotFound, GetAreaResponse{
					Status:  false,
					Message: "Area with the specified ID does not exist",
					Data:    []interface{}{},
				})
			}
			return c.JSON(http.StatusInternalServerError, GetAreaResponse{
				Status:  false,
				Message: err.Error(),
				Data:    []interface{}{},
			})
		}

		model := &GetAreaModel{
			ID:        output.Data.ID,
			Label:     output.Data.Label,
			Enabled:   output.Data.Enabled,
			Acronym:   output.Data.Acronym,
			CreatedAt: output.Data.CreatedAt,
			UpdatedAt: output.Data.UpdatedAt,
		}

		response := GetAreaResponse{
			Status:  true,
			Message: "Area retrieved successfully.",
			Data:    model,
		}
		return c.JSON(http.StatusOK, response)
	}
}
