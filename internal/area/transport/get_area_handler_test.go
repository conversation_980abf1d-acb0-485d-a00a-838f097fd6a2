package transport

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

type mockGetAreaUsecase struct{ mock.Mock }

func (m *mockGetAreaUsecase) Execute(ctx context.Context, id string) (*usecase.GetAreaOutput, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.GetAreaOutput), args.Error(1)
}

func TestGetAreaHandler_Handle(t *testing.T) {
	e := echo.New()

	t.Run("successful get area", func(t *testing.T) {
		mockUsecase := new(mockGetAreaUsecase)
		handler := NewGetAreaHandler(mockUsecase)

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		expected := &usecase.GetAreaOutput{
			Data: &usecase.GetAreaOutputModel{
				ID:        areaID,
				Label:     "Medicine",
				Enabled:   true,
				Acronym:   "MED",
				CreatedAt: "2025-09-04T10:00:00Z",
				UpdatedAt: "2025-09-04T10:00:00Z",
			},
		}
		mockUsecase.On("Execute", mock.Anything, areaID).Return(expected, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas/"+areaID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(areaID)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetAreaResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Status)
		assert.NotNil(t, response.Data)
		model, ok := response.Data.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, areaID, model["id"])
		mockUsecase.AssertExpectations(t)
	})

	t.Run("missing id param", func(t *testing.T) {
		mockUsecase := new(mockGetAreaUsecase)
		handler := NewGetAreaHandler(mockUsecase)

		req := httptest.NewRequest(http.MethodGet, "/areas/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		var response GetAreaResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Invalid area ID format", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})

	t.Run("area not found", func(t *testing.T) {
		mockUsecase := new(mockGetAreaUsecase)
		handler := NewGetAreaHandler(mockUsecase)

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		notFoundErr := errors.New("Area with the specified ID does not exist")
		mockUsecase.On("Execute", mock.Anything, areaID).Return(nil, notFoundErr)

		req := httptest.NewRequest(http.MethodGet, "/areas/"+areaID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(areaID)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
		var response GetAreaResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Equal(t, "Area with the specified ID does not exist", response.Message)
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})
}
