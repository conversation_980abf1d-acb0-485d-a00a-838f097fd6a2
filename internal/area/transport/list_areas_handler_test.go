package transport

import (
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

type mockListAreaUsecase struct{}

func (m *mockListAreaUsecase) Execute(ctx context.Context, params repository.ListAreaFilter) (*usecase.ListAreaOutput, int64, error) {
	return &usecase.ListAreaOutput{Data: []*usecase.ListAreaOutputModel{}}, 0, nil
}

func TestListAreasHandler_Handle(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest("GET", "/areas", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	h := NewListAreasHandler(&mockListAreaUsecase{})
	err := h.<PERSON>le()(c)
	assert.NoError(t, err)
	assert.Equal(t, 200, rec.Code)

	var response ListAreasResponse
	_ = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.True(t, response.Status)
	assert.Equal(t, "Areas retrieved successfully.", response.Message)
	assert.NotNil(t, response.Data)
	assert.Equal(t, 0, len(response.Data.([]interface{})))
}
