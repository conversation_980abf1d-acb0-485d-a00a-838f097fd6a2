package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestArea_Creation(t *testing.T) {
	t.Run("valid area creation", func(t *testing.T) {
		id := core.NewID()
		now := time.Now()
		input := &NewAreaInput{
			ID:        id.Value(),
			Label:     "Medicine",
			Enabled:   true,
			Acronym:   "MED",
			CreatedAt: &now,
			UpdatedAt: nil,
		}
		area, err := NewArea(input)
		assert.NoError(t, err)
		if assert.NotNil(t, area) {
			assert.Equal(t, id.Value(), area.ID())
			assert.Equal(t, "Medicine", area.Label())
			assert.True(t, area.Enabled())
			assert.Equal(t, "MED", area.Acronym())
		}
	})

	t.Run("area with disabled status", func(t *testing.T) {
		id := core.NewID()
		now := time.Now()
		input := &NewAreaInput{
			ID:        id.Value(),
			Label:     "Dentistry",
			Enabled:   false,
			Acronym:   "DENT",
			CreatedAt: &now,
			UpdatedAt: nil,
		}
		area, err := NewArea(input)
		assert.NoError(t, err)
		if assert.NotNil(t, area) {
			assert.Equal(t, id.Value(), area.ID())
			assert.Equal(t, "Dentistry", area.Label())
			assert.False(t, area.Enabled())
			assert.Equal(t, "DENT", area.Acronym())
		}
	})
}

func TestArea_ZeroValues(t *testing.T) {
	now := time.Now()
	input := &NewAreaInput{
		ID:        "",
		Label:     "",
		Enabled:   false,
		Acronym:   "",
		CreatedAt: &now,
		UpdatedAt: nil,
	}
	area, err := NewArea(input)
	assert.Error(t, err) // Should error due to missing required fields
	assert.Nil(t, area)
}
