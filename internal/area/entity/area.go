package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// Area represents a medical area (e.g., Medicine, Dentistry)
type Area struct {
	id        core.Identifier
	label     string
	enabled   bool
	acronym   string
	createdAt *core.Timestamp
	updatedAt *core.Timestamp
}

type NewAreaInput struct {
	ID              string
	Label           string
	Enabled         bool
	Acronym         string
	CreatedAt       *time.Time
	UpdatedAt       *time.Time
}

func NewArea(input *NewAreaInput) (*Area, error) {
	if input == nil {
		panic("NewAreaInput cannot be nil")
	}

	// Parse and validate ID
	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, err
	}

	// Validate required fields
	if input.Label == "" {
		return nil, core.NewBusinessError("area label is required")
	}
	if input.Acronym == "" {
		return nil, core.NewBusinessError("area acronym is required")
	}
	if input.CreatedAt == nil && input.UpdatedAt == nil {
		return nil, core.NewBusinessError("at least one timestamp is required")
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdTimestamp, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid created timestamp: %v", err)
	}

	updatedTimestamp, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid updated timestamp: %v", err)
	}

	return &Area{
		id:        *id,
		label:     input.Label,
		acronym:   input.Acronym,
		enabled:   input.Enabled,
		createdAt: createdTimestamp,
		updatedAt: updatedTimestamp,
	}, nil
}

// Getter methods
func (a *Area) ID() string {
	return a.id.Value()
}

func (a *Area) Label() string {
	return a.label
}

func (a *Area) Acronym() string {
	return a.acronym
}

func (a *Area) Enabled() bool {
	return a.enabled
}

func (a *Area) CreatedAt() time.Time {
	return a.createdAt.Value()
}

func (a *Area) UpdatedAt() time.Time {
	return a.updatedAt.Value()
}