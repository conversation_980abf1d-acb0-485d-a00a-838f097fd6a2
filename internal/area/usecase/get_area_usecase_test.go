package usecase

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
)

type mockRepoGetArea struct{}

func (m *mockRepoGetArea) GetArea(ctx context.Context, id string) (*entity.Area, error) {
	input := &entity.NewAreaInput{
		ID:        "84ce4d70-a442-412b-bf27-06f4544a8661",
		Label:     "Test",
		Acronym:   "TST",
		Enabled:   true,
		CreatedAt: ptrTime(time.Now()),
		UpdatedAt: ptrTime(time.Now()),
	}
	area, err := entity.NewArea(input)
	if err != nil {
		return nil, err
	}
	return area, nil
}

func TestAreaUsecase_GetArea_Split(t *testing.T) {
	uc := &GetAreaUsecase{repo: &mockRepoGetArea{}}
	output, err := uc.Execute(context.Background(), "84ce4d70-a442-412b-bf27-06f4544a8661")
	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.Equal(t, "Test", output.Data.Label)
}
