package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var listAreaUsecaseName = "ListAreaUsecase"

type listAreaRepository interface {
	ListAreas(ctx context.Context, params repository.ListAreaFilter) ([]*entity.Area, int64, error)
}

type ListAreaUsecase struct {
	repo listAreaRepository
}

type ListAreaOutputModel struct {
	ID        string
	Label     string
	Enabled   bool
	Acronym   string
	CreatedAt string
	UpdatedAt string
}

type ListAreasInput struct {
	Page      int
	Size      int
	Enabled   *bool
	Acronym   *string
	Label     *string
	OrderBy   string
	Direction string
}

type ListAreaOutput struct {
	Data []*ListAreaOutputModel
}

func NewListAreaUsecase(repo listAreaRepository) *ListAreaUsecase {
	return &ListAreaUsecase{
		repo: repo,
	}
}

func (u *ListAreaUsecase) Execute(ctx context.Context, params repository.ListAreaFilter) (*ListAreaOutput, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", listAreaUsecaseName))
	logger.Debug("trying to execute")

	areas, total, err := u.repo.ListAreas(ctx, params)
	if err != nil {
		return nil, 0, err
	}

	var areaModels []*ListAreaOutputModel
	for _, area := range areas {
		if area == nil {
			areaModels = append(areaModels, nil)
			continue
		}
		areaModels = append(areaModels, &ListAreaOutputModel{
			ID:        area.ID(),
			Label:     area.Label(),
			Acronym:   area.Acronym(),
			Enabled:   area.Enabled(),
			CreatedAt: area.CreatedAt().Format("2006-01-02T15:04:05Z"),
			UpdatedAt: area.UpdatedAt().Format("2006-01-02T15:04:05Z"),
		})
	}

	return &ListAreaOutput{
		Data: areaModels,
	}, total, nil
}
