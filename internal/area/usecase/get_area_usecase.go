package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var getAreaUsecaseName = "GetAreaUsecase"

type getAreaRepository interface {
	GetArea(ctx context.Context, id string) (*entity.Area, error)
}

type GetAreaUsecase struct {
	repo getAreaRepository
}

type GetAreaOutputModel struct {
	ID        string
	Label     string
	Enabled   bool
	Acronym   string
	CreatedAt string
	UpdatedAt string
}

type GetAreaInput struct {
	ID string
}

type GetAreaOutput struct {
	Data *GetAreaOutputModel
}

func NewGetAreaUsecase(repo getAreaRepository) *GetAreaUsecase {
	return &GetAreaUsecase{
		repo: repo,
	}
}

func (u *GetAreaUsecase) Execute(ctx context.Context, id string) (*GetAreaOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", getAreaUsecaseName))
	logger.Debug("trying to execute")

	area, err := u.repo.GetArea(ctx, id)
	if err != nil {
		return nil, err
	}

	output := &GetAreaOutput{
		Data: &GetAreaOutputModel{
			ID:        area.ID(),
			Label:     area.Label(),
			Acronym:   area.Acronym(),
			Enabled:   area.Enabled(),
			CreatedAt: area.CreatedAt().Format("2006-01-02T15:04:05Z"),
			UpdatedAt: area.UpdatedAt().Format("2006-01-02T15:04:05Z"),
		},
	}
	return output, nil
}
