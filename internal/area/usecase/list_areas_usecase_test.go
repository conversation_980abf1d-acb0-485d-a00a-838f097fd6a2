package usecase

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
)

type mockRepo struct{}

func ptrTime(t time.Time) *time.Time {
	return &t
}

func (m *mockRepo) ListAreas(ctx context.Context, params repository.ListAreaFilter) ([]*entity.Area, int64, error) {
	input := &entity.NewAreaInput{
		ID:        "84ce4d70-a442-412b-bf27-06f4544a8661",
		Label:     "Test",
		Acronym:   "TST",
		Enabled:   true,
		CreatedAt: ptrTime(time.Now()),
		UpdatedAt: ptrTime(time.Now()),
	}
	area, err := entity.NewArea(input)
	if err != nil {
		return nil, 0, err
	}
	return []*entity.Area{area}, 1, nil
}

func TestAreaUsecase_ListAreas_Split(t *testing.T) {
	uc := NewListAreaUsecase(&mockRepo{})
	output, pagination, err := uc.Execute(context.Background(), repository.ListAreaFilter{})
	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.NotNil(t, pagination)
	assert.Equal(t, "Test", output.Data[0].Label)
}
