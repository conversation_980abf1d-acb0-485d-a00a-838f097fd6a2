package usecase_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/service"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

// MockAreaUpsertRepository is a mock implementation of the AreaUpsertRepository interface
type MockAreaUpsertRepository struct {
	mock.Mock
}

func (m *MockAreaUpsertRepository) UpsertArea(ctx context.Context, area *entity.Area) error {
	args := m.Called(ctx, area)
	return args.Error(0)
}

// MockAreaValidationService is a mock implementation of the AreaValidationService interface
type MockAreaValidationService struct {
	mock.Mock
}

func (m *MockAreaValidationService) ValidateLabelAndUUID(ctx context.Context, label string, uuid string) error {
	args := m.Called(ctx, label, uuid)
	return args.Error(0)
}

func (m *MockAreaValidationService) ValidateBusinessRules(ctx context.Context, area *entity.Area) error {
	args := m.Called(ctx, area)
	return args.Error(0)
}

func (m *MockAreaValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func TestUpsertAreaUsecase_Execute(t *testing.T) {
	validID := core.NewID()
	testTime := time.Now()
	tests := []struct {
		name           string
		input          *usecase.UpsertAreaInput
		setupMocks     func(*MockAreaUpsertRepository, *MockAreaValidationService)
		wantErr        bool
		wantErrMessage string
		checkResult    func(*testing.T, *usecase.UpsertAreaOutput)
	}{
		{
			name: "Successful upsert with existing ID",
			input: &usecase.UpsertAreaInput{
				ID:        core.ToPtr(validID.Value()),
				Label:     "Medicine",
				Acronym:   "MED",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				// Validation service succeeds
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)

				// Repository succeeds
				repo.On("UpsertArea", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)
			},
			wantErr: false,
			checkResult: func(t *testing.T, output *usecase.UpsertAreaOutput) {
				assert.Equal(t, validID.Value(), output.ID)
				assert.Equal(t, "Medicine", output.Label)
				assert.Equal(t, "MED", output.Acronym)
				assert.True(t, output.Enabled)
			},
		},
		{
			name: "Successful upsert without ID (generates new ID)",
			input: &usecase.UpsertAreaInput{
				Label:     "Dentistry",
				Acronym:   "DENT",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				// Validation service succeeds
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)

				// Repository succeeds
				repo.On("UpsertArea", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)
			},
			wantErr: false,
			checkResult: func(t *testing.T, output *usecase.UpsertAreaOutput) {
				assert.NotEmpty(t, output.ID)
				assert.Equal(t, "Dentistry", output.Label)
				assert.Equal(t, "DENT", output.Acronym)
				assert.True(t, output.Enabled)
			},
		},
		{
			name:           "Nil input",
			input:          nil,
			setupMocks:     func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
		{
			name: "Empty label",
			input: &usecase.UpsertAreaInput{
				Label:   "",
				Acronym: "MED",
				Enabled: true,
			},
			setupMocks:     func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {},
			wantErr:        true,
			wantErrMessage: "label is required",
		},
		{
			name: "Empty acronym",
			input: &usecase.UpsertAreaInput{
				Label:   "Medicine",
				Acronym: "",
				Enabled: true,
			},
			setupMocks:     func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {},
			wantErr:        true,
			wantErrMessage: "acronym is required",
		},
		{
			name: "Invalid UUID format",
			input: &usecase.UpsertAreaInput{
				ID:      core.ToPtr("invalid-uuid"),
				Label:   "Medicine",
				Acronym: "MED",
				Enabled: true,
			},
			setupMocks:     func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Service validation fails",
			input: &usecase.UpsertAreaInput{
				Label:     "Medicine",
				Acronym:   "MED",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(errors.New("validation error"))
			},
			wantErr:        true,
			wantErrMessage: "validation error",
		},
		{
			name: "Business rules validation fails",
			input: &usecase.UpsertAreaInput{
				Label:     "Medicine",
				Acronym:   "MED",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(errors.New("business rule violation"))
			},
			wantErr:        true,
			wantErrMessage: "business rule violation",
		},

		{
			name: "Repository upsert fails with business error",
			input: &usecase.UpsertAreaInput{
				Label:     "Medicine",
				Acronym:   "MED",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)

				businessErr := core.NewBusinessError("Area acronym 'MED' already exists")
				repo.On("UpsertArea", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(businessErr)
			},
			wantErr:        true,
			wantErrMessage: "Area acronym 'MED' already exists",
		},
		{
			name: "Repository upsert fails with generic error",
			input: &usecase.UpsertAreaInput{
				Label:     "Medicine",
				Acronym:   "MED",
				Enabled:   true,
				UpdatedAt: &testTime,
			},
			setupMocks: func(repo *MockAreaUpsertRepository, validation *MockAreaValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(nil)

				repo.On("UpsertArea", mock.Anything, mock.AnythingOfType("*entity.Area")).Return(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database error occurred while upserting area",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(MockAreaUpsertRepository)
			mockValidation := new(MockAreaValidationService)
			tt.setupMocks(mockRepo, mockValidation)

			usecase := usecase.NewUpsertAreaUsecase(mockRepo, mockValidation)
			result, err := usecase.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.checkResult != nil {
					tt.checkResult(t, result)
				}
			}

			mockRepo.AssertExpectations(t)
			mockValidation.AssertExpectations(t)
		})
	}
}

func TestNewUpsertAreaUsecase(t *testing.T) {
	mockRepo := new(MockAreaUpsertRepository)
	mockValidation := new(MockAreaValidationService)

	upsertUsecase := usecase.NewUpsertAreaUsecase(mockRepo, mockValidation)

	assert.NotNil(t, upsertUsecase)
	assert.IsType(t, &usecase.UpsertAreaUsecase{}, upsertUsecase)
}
