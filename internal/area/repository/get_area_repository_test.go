package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAreaRepository_GetByID_Split(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAreaRepository(db)
	ctx := context.Background()
	columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
	areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(areaID, "Medicine", true, "MED", now, now)
	mock.ExpectQuery(`SELECT id, label, enabled, acronym, created_at, updated_at FROM area WHERE id = \$1`).WithArgs(areaID).WillReturnRows(row)

	area, err := repo.GetArea(ctx, areaID)
	require.NoError(t, err)
	assert.NotNil(t, area)
	assert.Equal(t, areaID, area.ID())
}
