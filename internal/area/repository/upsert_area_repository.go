package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// AreaUpsertRepository defines the interface for area upsert operations
type AreaUpsertRepository interface {
	UpsertArea(ctx context.Context, area *entity.Area) error
}

// areaUpsertRepository implements AreaUpsertRepository
type areaUpsertRepository struct {
	db *sqlx.DB
}

// NewAreaUpsertRepository creates a new area upsert repository
func NewAreaUpsertRepository(db *sqlx.DB) *areaUpsertRepository {
	return &areaUpsertRepository{
		db: db,
	}
}

// UpsertArea upserts an area entity into the database
func (r *areaUpsertRepository) UpsertArea(ctx context.Context, area *entity.Area) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "AreaRepository"), vlog.F("method", "UpsertArea"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.area WHERE id = $1`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("id", area.ID()))
	err := r.db.QueryRowContext(ctx, checkQuery, area.ID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing area", vlog.F("error", err))
		return fmt.Errorf("failed to check existing area: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", area.UpdatedAt()),
			vlog.F("incoming_is_newer", area.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && area.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("area event discarded - older than existing record",
			vlog.F("id", area.ID()),
			vlog.F("label", area.Label()),
			vlog.F("event_updated_at", area.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.area (id, label, acronym, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (id) DO UPDATE SET
			label = EXCLUDED.label,
			acronym = EXCLUDED.acronym,
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.area.updated_at
	`

	args := []interface{}{
		area.ID(),
		area.Label(),
		area.Acronym(),
		area.Enabled(),
		area.CreatedAt(),
		area.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)
	if err != nil {
		// Check for acronym constraint violation
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr), vlog.F("area", area.Acronym()))
			return parsedErr
		}

		logger.Error("failed to upsert area", vlog.F("error", err))
		return fmt.Errorf("failed to upsert area: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("area updated successfully",
			vlog.F("id", area.ID()),
			vlog.F("label", area.Label()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("area inserted successfully",
			vlog.F("id", area.ID()),
			vlog.F("label", area.Label()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
