package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewAreaUpsertRepository(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db)

	assert.NotNil(t, repo)
}

func TestAreaUpsertRepository_UpsertArea(t *testing.T) {
	now := time.Now().UTC()

	tests := []struct {
		name           string
		setupEntity    func() *entity.Area
		setupMock      func(sqlmock.Sqlmock, string)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful upsert - INSERT",
			setupEntity: func() *entity.Area {
				validID := core.NewID()
				input := &entity.NewAreaInput{
					ID:        validID.Value(),
					Label:     "Medicine",
					Acronym:   "MED",
					Enabled:   true,
					CreatedAt: &now,
					UpdatedAt: &now,
				}
				area, _ := entity.NewArea(input)
				return area
			},
			setupMock: func(mock sqlmock.Sqlmock, id string) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.area WHERE id`).
					WithArgs(id).
					WillReturnError(sql.ErrNoRows)

				// Expect successful INSERT
				expectedQuery := `INSERT INTO public\.area.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(id, "Medicine", "MED", true, now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name: "Database error",
			setupEntity: func() *entity.Area {
				validID := core.NewID()
				input := &entity.NewAreaInput{
					ID:        validID.Value(),
					Label:     "Medicine",
					Acronym:   "MED",
					Enabled:   true,
					CreatedAt: &now,
					UpdatedAt: &now,
				}
				area, _ := entity.NewArea(input)
				return area
			},
			setupMock: func(mock sqlmock.Sqlmock, id string) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.area WHERE id`).
					WithArgs(id).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.area.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(id, "Medicine", "MED", true, now, now).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name: "Acronym constraint violation",
			setupEntity: func() *entity.Area {
				validID := core.NewID()
				input := &entity.NewAreaInput{
					ID:        validID.Value(),
					Label:     "Medicine",
					Acronym:   "MED",
					Enabled:   true,
					CreatedAt: &now,
					UpdatedAt: &now,
				}
				area, _ := entity.NewArea(input)
				return area
			},
			setupMock: func(mock sqlmock.Sqlmock, id string) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.area WHERE id`).
					WithArgs(id).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.area.*ON CONFLICT.*`
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "area_acronym_key"`)
				mock.ExpectExec(expectedQuery).
					WithArgs(id, "Medicine", "MED", true, now, now).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "area_acronym_key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			area := tt.setupEntity()
			tt.setupMock(mock, area.ID())

			repo := repository.NewAreaUpsertRepository(db)
			err := repo.UpsertArea(context.Background(), area)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestAreaUpsertRepository_UpsertArea_DisabledArea(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	now := time.Now().UTC()
	validID := core.NewID()

	// Create a disabled area entity
	input := &entity.NewAreaInput{
		ID:        validID.Value(),
		Label:     "Medicine",
		Acronym:   "MED",
		Enabled:   false,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
	area, err := entity.NewArea(input)
	assert.NoError(t, err)

	mock.ExpectQuery(`SELECT updated_at FROM public\.area WHERE id`).
		WithArgs(validID.Value()).
		WillReturnError(sql.ErrNoRows)

	expectedQuery := `INSERT INTO public\.area.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Medicine", "MED", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAreaUpsertRepository(db)
	err = repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaUpsertRepository_UpsertArea_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create an area entity for update
	input := &entity.NewAreaInput{
		ID:        validID.Value(),
		Label:     "Updated Medicine",
		Acronym:   "UMED",
		Enabled:   false,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
	area, err := entity.NewArea(input)
	assert.NoError(t, err)

	olderTime := now.Add(-1 * time.Hour)
	mock.ExpectQuery(`SELECT updated_at FROM public\.area WHERE id`).
		WithArgs(validID.Value()).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&olderTime))

	expectedQuery := `INSERT INTO public\.area.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Updated Medicine", "UMED", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAreaUpsertRepository(db)
	err = repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
