package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}
func TestAreaRepository_ListAreas(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAreaRepository(db)
	ctx := context.Background()
	columns := []string{"id", "label", "acronym", "enabled", "created_at", "updated_at"}
	now := time.Now()
	rows := sqlmock.NewRows(columns).
		AddRow("84ce4d70-a442-412b-bf27-06f4544a8661", "Medicine", "MED", true, now, now).
		AddRow("4beed17b-a38a-4da1-8b26-94d2f1513001", "Dentistry", "DENT", true, now, now).
		AddRow("e117dcf1-4acd-499f-80d2-7c868f23d6d0", "Psychology", "PSY", false, now, now)
	mock.ExpectQuery(`SELECT id, label, acronym, enabled, created_at, updated_at FROM area WHERE 1=1 ORDER BY label ASC`).WillReturnRows(rows)
	mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))

	params := ListAreaFilter{}
	areas, total, err := repo.ListAreas(ctx, params)
	require.NoError(t, err)
	assert.Equal(t, int64(3), total)
	assert.Len(t, areas, 3)
}
