package repository

import (
	"context"
	"time"

	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *AreaRepository) GetArea(ctx context.Context, id string) (*entity.Area, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetArea"), vlog.F("action", "get area by id"))

	query := `SELECT id, label, enabled, acronym, created_at, updated_at FROM area WHERE id = $1`
	args := []interface{}{id}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))
	var idStr, label, acronym string
	var enabled bool
	var createdAtRaw, updatedAtRaw time.Time
	row := r.db.QueryRowxContext(ctx, query, id)
	if err := row.Scan(&idStr, &label, &enabled, &acronym, &createdAtRaw, &updatedAtRaw); err != nil {
		logger.Error("failed to scan area row: " + err.Error())
		return nil, err
	}
	input := &entity.NewAreaInput{
		ID:        idStr,
		Label:     label,
		Acronym:   acronym,
		CreatedAt: &createdAtRaw,
		UpdatedAt: &updatedAtRaw,
	}
	area, err := entity.NewArea(input)
	if err != nil {
		logger.Error("failed to construct entity from DB", vlog.F("error", err), vlog.F("db_id", idStr))
		return nil, err
	}
	return area, nil
}