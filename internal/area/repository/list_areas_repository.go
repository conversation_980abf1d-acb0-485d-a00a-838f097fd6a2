package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AreaRepository struct {
	db *sqlx.DB
}

type ListAreaFilter struct {
	Page      int
	Size      int
	Enabled   *bool
	Acronym   *string
	Label     *string
	OrderBy   string
	Direction string
}

func NewAreaRepository(db *sqlx.DB) *AreaRepository {
	return &AreaRepository{db: db}
}

func (r *AreaRepository) ListAreas(ctx context.Context, params ListAreaFilter) ([]*entity.Area, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "ListAreas"), vlog.F("action", "list areas"))

	var (
		query      string
		countQuery string
		rows       *sqlx.Rows
		err        error
		args       []interface{}
		argIdx     = 1
	)

	query = "SELECT id, label, acronym, enabled, created_at, updated_at FROM area WHERE 1=1"
	countQuery = "SELECT COUNT(id) FROM area WHERE 1=1"

	filterArgs := []interface{}{}
	filterIdx := 1
	if params.Acronym != nil && *params.Acronym != "" {
		query += fmt.Sprintf(" AND acronym = $%d", argIdx)
		countQuery += fmt.Sprintf(" AND acronym = $%d", filterIdx)
		args = append(args, *params.Acronym)
		filterArgs = append(filterArgs, *params.Acronym)
		argIdx++
		filterIdx++
	}
	if params.Label != nil && *params.Label != "" {
		query += fmt.Sprintf(" AND LOWER(label) LIKE $%d", argIdx)
		countQuery += fmt.Sprintf(" AND LOWER(label) LIKE $%d", filterIdx)
		args = append(args, "%"+strings.ToLower(*params.Label)+"%")
		filterArgs = append(filterArgs, "%"+strings.ToLower(*params.Label)+"%")
		argIdx++
		filterIdx++
	}
	if params.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIdx)
		countQuery += fmt.Sprintf(" AND enabled = $%d", filterIdx)
		args = append(args, *params.Enabled)
		filterArgs = append(filterArgs, *params.Enabled)
		argIdx++
		filterIdx++
	}
	orderBy := "label"
	if params.OrderBy != "" {
		orderBy = params.OrderBy
	}
	direction := "ASC"
	if strings.ToUpper(params.Direction) == "DESC" {
		direction = "DESC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, direction)
	if params.Size > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIdx, argIdx+1)
		args = append(args, params.Size)
		offset := 0
		if params.Page > 0 {
			offset = params.Page * params.Size
		}
		args = append(args, offset)
	}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	rows, err = r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("failed to query areas: " + err.Error())
		return nil, 0, err
	}
	defer rows.Close()

	var output []*entity.Area
	for rows.Next() {
		var idStr, labelStr, acronymStr string
		var enabledBool bool
		var createdAtRaw, updatedAtRaw time.Time
		if err := rows.Scan(&idStr, &labelStr, &acronymStr, &enabledBool, &createdAtRaw, &updatedAtRaw); err != nil {
			logger.Error("failed to scan area row: " + err.Error())
			return nil, 0, err
		}
		input := &entity.NewAreaInput{
			ID:        idStr,
			Label:     labelStr,
			Acronym:   acronymStr,
			Enabled:   enabledBool,
			CreatedAt: &createdAtRaw,
			UpdatedAt: &updatedAtRaw,
		}
		area, err := entity.NewArea(input)
		if err != nil {
			logger.Error("failed to construct entity from DB", vlog.F("error", err), vlog.F("db_id", idStr))
			return nil, 0, err
		}
		output = append(output, area)
	}

	var total int64
	if err := r.db.GetContext(ctx, &total, countQuery, filterArgs...); err != nil {
		logger.Error("AreaRepository.ListAreas: failed to get total count", vlog.F("error", err))
		return nil, 0, err
	}

	return output, total, nil
}
