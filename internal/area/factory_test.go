package area

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewFactory(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLArea: "https://sqs.us-east-1.amazonaws.com/123456789/area-queue",
			Region:          "us-east-1",
			AccessKeyID:     "test-access-key",
			SecretAccessKey: "test-secret-key",
		},
	}

	logger := vlog.NewWithLevel("error")
	factory, err := NewFactory(db, cfg, logger)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.AreaEventListener)
}

func TestNewFactory_EmptyConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			// Empty AWS config
		},
	}

	logger := vlog.NewWithLevel("error")
	factory, err := NewFactory(db, cfg, logger)

	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, factory)
	} else {
		assert.NotNil(t, factory)
	}
}

func TestNewFactory_InvalidAWSConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLArea: "invalid-url",
			Region:          "",
			AccessKeyID:     "",
			SecretAccessKey: "",
		},
	}

	// This should fail due to invalid AWS configuration
	factory, err := NewFactory(db, cfg, vlog.NewWithLevel("error"))

	// Should fail with invalid config
	assert.Error(t, err)
	assert.Nil(t, factory)
}

func TestFactory_Structure(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory structure test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLArea: "https://sqs.us-east-1.amazonaws.com/123456789/area-queue",
			Region:          "us-east-1",
			AccessKeyID:     "test-access-key",
			SecretAccessKey: "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg, vlog.NewWithLevel("error"))
	assert.NoError(t, err)

	// Verify factory structure
	assert.NotNil(t, factory)

	// Test that all required services are available
	assert.NotNil(t, factory.AreaEventListener)
}

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestFactory_ListAndGetAreaHandler(t *testing.T) {
	db, _ := setupMockDB(t)
	cfg := config.Configuration{}
	logger := vlog.NewWithLevel("error")
	factory, err := NewFactory(db, cfg, logger)
	require.NoError(t, err)
	assert.NotNil(t, factory)

	hList := factory.ListAreaHandler()
	assert.NotNil(t, hList)
	assert.IsType(t, &transport.ListAreasHandler{}, hList)

	hGet := factory.GetAreaHandler()
	assert.NotNil(t, hGet)
	assert.IsType(t, &transport.GetAreaHandler{}, hGet)
}
