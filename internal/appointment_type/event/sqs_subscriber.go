package event

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSAppointmentTypeEventSubscriber implements AppointmentTypeEventSubscriber using AWS SQS
type SQSAppointmentTypeEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes an appointment type event message
		ProcessMessage(ctx context.Context, message *AppointmentTypeEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSAppointmentTypeEventSubscriber creates a new SQS-based appointment type event subscriber
func NewSQSAppointmentTypeEventSubscriber(cfg config.Configuration, queueURL string) (*SQSAppointmentTypeEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS client: %w", err)
	}

	return &SQSAppointmentTypeEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("subscriber", "AppointmentTypeEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe sets the processor for handling appointment type events
func (s *SQSAppointmentTypeEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *AppointmentTypeEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start begins listening for appointment type events from SQS
func (s *SQSAppointmentTypeEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	if s.isRunning {
		s.mutex.Unlock()
		return fmt.Errorf("appointment type event subscriber is already running")
	}
	s.isRunning = true
	s.mutex.Unlock()

	s.logger.Info("starting appointment type event subscriber")

	s.wg.Add(1)
	go s.pollMessages(ctx)

	return nil
}

// Stop stops the appointment type event subscriber
func (s *SQSAppointmentTypeEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("appointment type event subscriber is not running")
	}

	s.logger.Info("stopping appointment type event subscriber")
	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	return nil
}

// IsRunning returns whether the subscriber is currently running
func (s *SQSAppointmentTypeEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSAppointmentTypeEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("appointment type event subscriber stopped")
			return
		case <-ticker.C:
			s.processMessages(ctx)
		}
	}
}

// processMessages processes messages from SQS
func (s *SQSAppointmentTypeEventSubscriber) processMessages(ctx context.Context) {
	if s.processor == nil {
		s.logger.Warn("no processor set for appointment type event subscriber")
		return
	}

	result, err := s.sqsClient.ReceiveMessages(ctx, 10, 20)
	if err != nil {
		s.logger.Error("failed to receive messages from appointment type SQS", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	for _, message := range result.Messages {
		if err := s.processMessage(ctx, message); err != nil {
			s.logger.Error("failed to process appointment type message", vlog.F("error", err), vlog.F("messageId", *message.MessageId))
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSAppointmentTypeEventSubscriber) processMessage(ctx context.Context, sqsMessage types.Message) error {
	if sqsMessage.Body == nil {
		return fmt.Errorf("message body is nil")
	}

	// Parse the appointment type event message
	var eventMessage AppointmentTypeEventMessage
	if err := json.Unmarshal([]byte(*sqsMessage.Body), &eventMessage); err != nil {
		s.logger.Error("failed to unmarshal appointment type event message", vlog.F("error", err), vlog.F("body", *sqsMessage.Body))
		return fmt.Errorf("failed to unmarshal appointment type event message: %w", err)
	}

	// Process the message using the registered processor
	if err := s.processor.ProcessMessage(ctx, &eventMessage); err != nil {
		// Check if this is a business error
		var businessErr *core.BusinessError
		if errors.As(err, &businessErr) {
			s.logger.Error("business error occurred - deleting message to prevent retries",
				vlog.F("error", err),
				vlog.F("messageId", *sqsMessage.MessageId),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("action", "DELETE_MESSAGE"))

			// Delete the message to prevent retries (SQS will move to DLQ if configured)
			if _, deleteErr := s.sqsClient.DeleteMessage(ctx, *sqsMessage.ReceiptHandle); deleteErr != nil {
				s.logger.Error("failed to delete business error message from appointment type SQS",
					vlog.F("error", deleteErr),
					vlog.F("messageId", *sqsMessage.MessageId),
					vlog.F("original_error", err))
				return fmt.Errorf("failed to delete business error message from appointment type SQS: %w", deleteErr)
			}

			s.logger.Info("business error message deleted - will not be retried",
				vlog.F("messageId", *sqsMessage.MessageId),
				vlog.F("original_error", err))
			return nil
		}

		// This is a transient error - let SQS retry
		s.logger.Error("transient error occurred - message will be retried by appointment type SQS",
			vlog.F("error", err),
			vlog.F("messageId", *sqsMessage.MessageId),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("action", "RETRY"))

		return fmt.Errorf("transient error - message will be retried: %w", err)
	}

	// Delete the message from SQS after successful processing
	if _, err := s.sqsClient.DeleteMessage(ctx, *sqsMessage.ReceiptHandle); err != nil {
		s.logger.Error("failed to delete appointment type message from appointment type SQS", vlog.F("error", err), vlog.F("messageId", *sqsMessage.MessageId))
		return fmt.Errorf("failed to delete appointment type message from appointment type SQS: %w", err)
	}

	s.logger.Debug("successfully processed and deleted appointment type message", vlog.F("messageId", *sqsMessage.MessageId))
	return nil
}
