package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAppointmentTypeUsecase defines the interface for appointment type upsert operations
type UpsertAppointmentTypeUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertAppointmentTypeInput) (*usecase.UpsertAppointmentTypeOutput, error)
}

// AppointmentTypeUpsertEventProcessor processes appointment type upsert events
type AppointmentTypeUpsertEventProcessor struct {
	upsertUsecase UpsertAppointmentTypeUsecase
}

// NewAppointmentTypeUpsertEventProcessor creates a new appointment type upsert event processor
func NewAppointmentTypeUpsertEventProcessor(upsertUsecase UpsertAppointmentTypeUsecase) *AppointmentTypeUpsertEventProcessor {
	return &AppointmentTypeUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// IsBusinessError determines if an error is a business logic error that should go to DLQ
func (p *AppointmentTypeUpsertEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"appointment type name",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"appointment_type_name_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	// Check for core business errors
	var businessErr *core.BusinessError
	if errors.As(err, &businessErr) {
		return true
	}

	return false
}

// ProcessMessage processes an appointment type event message
func (p *AppointmentTypeUpsertEventProcessor) ProcessMessage(ctx context.Context, message *AppointmentTypeEventMessage) error {
	logger := vlog.New().With(vlog.F("processor", "AppointmentTypeUpsertEventProcessor"))

	if message == nil {
		logger.Error("validation failed", vlog.F("error", "message is required"))
		return fmt.Errorf("message is required")
	}

	// Validate required fields
	if message.AppointmentType == "" {
		logger.Error("validation failed", vlog.F("error", "appointment type name is required"))
		return fmt.Errorf("appointment type name is required in message data")
	}

	// Validate UUID format if provided
	if message.ID != nil && *message.ID != "" {
		if _, err := core.NewIDFromString(*message.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.ID))
			return fmt.Errorf("invalid UUID format in message data")
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertAppointmentTypeInput{
		AppointmentType: message.AppointmentType,
		Enabled:         message.Enabled,
		CreatedAt:       message.CreatedAt,
		UpdatedAt:       message.UpdatedAt,
	}

	if message.ID != nil {
		upsertInput.ID = message.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("appointment_type", message.AppointmentType),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			return core.NewBusinessError("Business error processing appointment type '%s': %v", message.AppointmentType, err)
		}

		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("appointment_type", message.AppointmentType),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing appointment type upsert: %w", err)
	}

	logger.Info("successfully processed appointment type upsert", vlog.F("appointment_type", message.AppointmentType))
	return nil
}
