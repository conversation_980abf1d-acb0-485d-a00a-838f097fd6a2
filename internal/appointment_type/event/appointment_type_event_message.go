package event

import (
	"time"
)

// AppointmentTypeEventMessage represents the complete event message structure
type AppointmentTypeEventMessage struct {
	ID              *string    `json:"id,omitempty"`
	AppointmentType string     `json:"appointment_type"`
	Enabled         bool       `json:"enabled"`
	CreatedAt       *time.Time `json:"created_at,omitempty"`
	UpdatedAt       *time.Time `json:"updated_at,omitempty"`
}
