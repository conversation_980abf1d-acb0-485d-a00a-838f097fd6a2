package transport

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

type getAppointmentTypeUsecase interface {
	Execute(ctx context.Context, id string) (*usecase.GetAppointmentTypeOutput, error)
}

type GetAppointmentTypeHandler struct {
	usecase getAppointmentTypeUsecase
}

type GetAppointmentTypeModel struct {
	UUID            string `json:"uuid"`
	AppointmentType string `json:"appointment_type"`
	Enabled         bool   `json:"enabled"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

type GetAppointmentTypeResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func NewGetAppointmentTypeHandler(uc getAppointmentTypeUsecase) *GetAppointmentTypeHandler {
	return &GetAppointmentTypeHandler{usecase: uc}
}

func (h *GetAppointmentTypeHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		id := c.Param("id")
		if id == "" {
			resp := GetAppointmentTypeResponse{
				Status:  false,
				Message: "Invalid appointment type ID format",
				Data:    []interface{}{},
			}
			return c.JSON(http.StatusBadRequest, resp)
		}

		output, err := h.usecase.Execute(c.Request().Context(), id)
		if err != nil {
			if strings.Contains(err.Error(), "invalid input syntax for type uuid") {
				resp := GetAppointmentTypeResponse{
					Status:  false,
					Message: "Invalid appointment type ID format",
					Data:    []interface{}{},
				}
				return c.JSON(http.StatusBadRequest, resp)
			}
			if err.Error() == "sql: no rows in result set" ||
				err.Error() == "Appointment type not found" ||
				strings.Contains(err.Error(), "Appointment type not found") {
				resp := GetAppointmentTypeResponse{
					Status:  false,
					Message: "Appointment type with the specified ID does not exist",
					Data:    []interface{}{},
				}
				return c.JSON(http.StatusNotFound, resp)
			}
			resp := GetAppointmentTypeResponse{
				Status:  false,
				Message: "Internal server error",
				Data:    []interface{}{},
			}
			return c.JSON(http.StatusInternalServerError, resp)
		}
		if output == nil || output.Data == nil {
			resp := GetAppointmentTypeResponse{
				Status:  false,
				Message: "Appointment type with the specified ID does not exist",
				Data:    []interface{}{},
			}
			return c.JSON(http.StatusNotFound, resp)
		}

		model := &GetAppointmentTypeModel{
			UUID:            output.Data.UUID,
			AppointmentType: output.Data.AppointmentType,
			Enabled:         output.Data.Enabled,
			CreatedAt:       output.Data.CreatedAt,
			UpdatedAt:       output.Data.UpdatedAt,
		}

		resp := GetAppointmentTypeResponse{
			Status:  true,
			Message: "Appointment type retrieved successfully.",
			Data:    model,
		}
		return c.JSON(http.StatusOK, resp)
	}
}
