package transport

import (
	"context"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

type listAppointmentTypeUsecase interface {
	Execute(ctx context.Context, enabled *bool, page, size int) (*usecase.ListAppointmentTypeOutput, int64, error)
}

type ListAppointmentTypesHandler struct {
	usecase listAppointmentTypeUsecase
}

type ListAppointmentTypeModel struct {
	UUID            string `json:"uuid"`
	AppointmentType string `json:"appointment_type"`
	Enabled         bool   `json:"enabled"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

type ListAppointmentTypesResponse struct {
	Status    bool                        `json:"status"`
	Message   string                      `json:"message"`
	Data      []*ListAppointmentTypeModel `json:"data"`
	Pagination map[string]interface{}      `json:"pagination"`
}

func NewListAppointmentTypesHandler(uc listAppointmentTypeUsecase) *ListAppointmentTypesHandler {
	return &ListAppointmentTypesHandler{usecase: uc}
}

func (h *ListAppointmentTypesHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		page, err := strconv.Atoi(c.QueryParam("page"))
		if err != nil || page < 1 {
			page = 1
		}
		size, err := strconv.Atoi(c.QueryParam("size"))
		if err != nil || size <= 0 {
			size = 10
		}
		if size > 100 {
			size = 100
		}

		enabledStr := c.QueryParam("enabled")
		var enabled *bool
		if enabledStr != "" {
			val, err := strconv.ParseBool(enabledStr)
			if err == nil {
				enabled = &val
			} else {
				return c.JSON(http.StatusBadRequest, map[string]interface{}{
					"status":  false,
					"message": "Invalid query parameters provided",
					"data":    []interface{}{},
				})
			}
		} else {
			return c.JSON(http.StatusBadRequest, map[string]interface{}{
				"status":  false,
				"message": "Invalid query parameters provided",
				"data":    []interface{}{},
			})
		}

		output, total, err := h.usecase.Execute(c.Request().Context(), enabled, page, size)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, map[string]interface{}{
				"status":  false,
				"message": "Internal server error",
				"data":    []interface{}{},
			})
		}

		pagination := map[string]interface{}{
			"current_page": page,
			"page_size":    size,
			"total":        total,
			"total_pages":  (total + int64(size) - 1) / int64(size),
		}

		var data []*ListAppointmentTypeModel
		for _, item := range output.Data {
			data = append(data, &ListAppointmentTypeModel{
				UUID:            item.UUID,
				AppointmentType: item.AppointmentType,
				Enabled:         item.Enabled,
				CreatedAt:       item.CreatedAt,
				UpdatedAt:       item.UpdatedAt,
			})
		}

		response := ListAppointmentTypesResponse{
			Status:    true,
			Message:   "Appointment types retrieved successfully.",
			Data:      data,
			Pagination: pagination,
		}
		return c.JSON(http.StatusOK, response)
	}
}
