package transport

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

type MockGetAppointmentTypeUsecase struct {
	mock.Mock
}

func (m *MockGetAppointmentTypeUsecase) Execute(ctx context.Context, id string) (*usecase.GetAppointmentTypeOutput, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.GetAppointmentTypeOutput), args.Error(1)
}

func TestGetAppointmentTypeHandler_Handle(t *testing.T) {
	e := echo.New()

	t.Run("successful get appointment type", func(t *testing.T) {
		mockUsecase := new(MockGetAppointmentTypeUsecase)
		handler := NewGetAppointmentTypeHandler(mockUsecase)
		atID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		output := &usecase.GetAppointmentTypeOutput{
			Data: &usecase.GetAppointmentTypeOutputModel{
				UUID:            atID,
				AppointmentType: "Consultation",
				Enabled:         true,
				CreatedAt:       "2025-09-03T10:00:00Z",
				UpdatedAt:       "2025-09-03T10:00:00Z",
			},
		}
		mockUsecase.On("Execute", mock.Anything, atID).Return(output, nil)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/"+atID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(atID)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetAppointmentTypeResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, "Appointment type retrieved successfully.", response.Message)
		model, ok := response.Data.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, atID, model["uuid"])
		mockUsecase.AssertExpectations(t)
	})

	t.Run("missing id param", func(t *testing.T) {
		mockUsecase := new(MockGetAppointmentTypeUsecase)
		handler := NewGetAppointmentTypeHandler(mockUsecase)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		var response GetAppointmentTypeResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Contains(t, response.Message, "Invalid appointment type ID format")
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})

	t.Run("not found", func(t *testing.T) {
		mockUsecase := new(MockGetAppointmentTypeUsecase)
		handler := NewGetAppointmentTypeHandler(mockUsecase)
		atID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		mockUsecase.On("Execute", mock.Anything, atID).Return(&usecase.GetAppointmentTypeOutput{Data: nil}, nil)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/"+atID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("id")
		c.SetParamValues(atID)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, rec.Code)
		var response GetAppointmentTypeResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Contains(t, response.Message, "Appointment type with the specified ID does not exist")
		assert.Equal(t, 0, len(response.Data.([]interface{})))
	})
}
