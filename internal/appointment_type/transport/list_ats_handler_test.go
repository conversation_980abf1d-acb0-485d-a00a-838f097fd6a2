package transport

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

type mockListAppointmentTypeUsecase struct {
	mock.Mock
}

func (m *mockListAppointmentTypeUsecase) Execute(ctx context.Context, enabled *bool, page, size int) (*usecase.ListAppointmentTypeOutput, int64, error) {
	args := m.Called(ctx, enabled, page, size)
	return args.Get(0).(*usecase.ListAppointmentTypeOutput), args.Get(1).(int64), args.Error(2)
}

func TestListAppointmentTypesHandler_Handle(t *testing.T) {
	e := echo.New()

	t.Run("missing enabled param", func(t *testing.T) {
		mockUsecase := new(mockListAppointmentTypeUsecase)
		handler := NewListAppointmentTypesHandler(mockUsecase)
		req := httptest.NewRequest(http.MethodGet, "/appointment-types?page=1&size=10", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		var response ListAppointmentTypesResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Contains(t, response.Message, "Invalid query parameters provided")
	})

	t.Run("invalid enabled param", func(t *testing.T) {
		mockUsecase := new(mockListAppointmentTypeUsecase)
		handler := NewListAppointmentTypesHandler(mockUsecase)
		req := httptest.NewRequest(http.MethodGet, "/appointment-types?enabled=notabool", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		var response ListAppointmentTypesResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Status)
		assert.Contains(t, response.Message, "Invalid query parameters provided")
	})

	t.Run("successful list", func(t *testing.T) {
		mockUsecase := new(mockListAppointmentTypeUsecase)
		handler := NewListAppointmentTypesHandler(mockUsecase)
		enabled := true
		output := &usecase.ListAppointmentTypeOutput{
			Data: []*usecase.ListAppointmentTypeOutputModel{
				{
					UUID:            "969c3e7d-6853-46f3-b433-2b3f11d9e76e",
					AppointmentType: "Consultation",
					Enabled:         true,
					CreatedAt:       "2025-09-03T10:00:00Z",
					UpdatedAt:       "2025-09-03T10:00:00Z",
				},
			},
		}
		mockUsecase.On("Execute", mock.Anything, &enabled, 1, 10).Return(output, int64(1), nil)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types?enabled=true&page=1&size=10", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		var response ListAppointmentTypesResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, "Appointment types retrieved successfully.", response.Message)
		assert.NotNil(t, response.Data)
		assert.Equal(t, "969c3e7d-6853-46f3-b433-2b3f11d9e76e", response.Data[0].UUID)
		mockUsecase.AssertExpectations(t)
	})
}
