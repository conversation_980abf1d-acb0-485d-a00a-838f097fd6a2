package usecase

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
)

type mockGetAppointmentTypeRepo struct{}

func (m *mockGetAppointmentTypeRepo) GetAppointmentType(ctx context.Context, id string) (*entity.AppointmentType, error) {
	created := time.Date(2025, 9, 3, 10, 0, 0, 0, time.UTC)
	updated := time.Date(2025, 9, 3, 10, 0, 0, 0, time.UTC)
	appointmentType, _ := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              id,
		AppointmentType: "Test",
		Enabled:         true,
		CreatedAt:       &created,
		UpdatedAt:       &updated,
	})
	return appointmentType, nil
}
func (m *mockGetAppointmentTypeRepo) UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error {
	return nil
}

func TestGetAppointmentTypeUsecase_Execute(t *testing.T) {
	repo := &mockGetAppointmentTypeRepo{}
	uc := NewGetAppointmentTypeUsecase(repo)
	id := "84ce4d70-a442-412b-bf27-06f4544a8661" // valid UUID
	output, err := uc.Execute(context.Background(), id)
	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.NotNil(t, output.Data)
	assert.Equal(t, id, output.Data.UUID)
	assert.Equal(t, "Test", output.Data.AppointmentType)
	assert.True(t, output.Data.Enabled)
	assert.Equal(t, "2025-09-03T10:00:00Z", output.Data.CreatedAt)
	assert.Equal(t, "2025-09-03T10:00:00Z", output.Data.UpdatedAt)
}
