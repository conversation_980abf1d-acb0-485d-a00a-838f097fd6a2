package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/service"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

// MockAppointmentTypeUpsertRepository is a mock implementation of AppointmentTypeUpsertRepository
type MockAppointmentTypeUpsertRepository struct {
	mock.Mock
}

func (m *MockAppointmentTypeUpsertRepository) UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error {
	args := m.Called(ctx, appointmentType)
	return args.Error(0)
}

// MockAppointmentTypeValidationService is a mock implementation of AppointmentTypeValidationService
type MockAppointmentTypeValidationService struct {
	mock.Mock
}

func (m *MockAppointmentTypeValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockAppointmentTypeValidationService) ValidateBusinessRules(ctx context.Context, appointmentType *entity.AppointmentType) error {
	args := m.Called(ctx, appointmentType)
	return args.Error(0)
}

func TestNewUpsertAppointmentTypeUsecase(t *testing.T) {
	mockRepo := new(MockAppointmentTypeUpsertRepository)
	mockValidationService := new(MockAppointmentTypeValidationService)

	uc := usecase.NewUpsertAppointmentTypeUsecase(mockRepo, mockValidationService)

	assert.NotNil(t, uc)
}

func TestUpsertAppointmentTypeUsecase_Execute(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		input          *usecase.UpsertAppointmentTypeInput
		setupMocks     func(*MockAppointmentTypeUpsertRepository, *MockAppointmentTypeValidationService)
		wantErr        bool
		wantErrMessage string
		expectedOutput *usecase.UpsertAppointmentTypeOutput
	}{
		{
			name: "Successful upsert with ID",
			input: &usecase.UpsertAppointmentTypeInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Consultation",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
				mockRepo.On("UpsertAppointmentType", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertAppointmentTypeOutput{
				ID:              validID.Value(),
				AppointmentType: "Consultation",
				Enabled:         true,
			},
		},
		{
			name: "Successful upsert without ID",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "Follow-up",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
				mockRepo.On("UpsertAppointmentType", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertAppointmentTypeOutput{
				AppointmentType: "Follow-up",
				Enabled:         true,
			},
		},
		{
			name:           "Nil input",
			input:          nil,
			setupMocks:     func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
		{
			name: "Empty appointment type name",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks:     func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {},
			wantErr:        true,
			wantErrMessage: "appointment type name is required",
		},
		{
			name: "Invalid UUID format",
			input: &usecase.UpsertAppointmentTypeInput{
				ID:              core.ToPtr("invalid-uuid"),
				AppointmentType: "Consultation",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks:     func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Service validation error",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "Test",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(errors.New("service validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "service validation failed",
		},
		{
			name: "Business rules validation error",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "Test",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(errors.New("business rules validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "business rules validation failed",
		},
		{
			name: "Repository error",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "Test",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
				mockRepo.On("UpsertAppointmentType", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "database error occurred while upserting appointment type",
		},
		{
			name: "Name constraint violation",
			input: &usecase.UpsertAppointmentTypeInput{
				AppointmentType: "duplicate-name",
				Enabled:         true,
				CreatedAt:       nil,
				UpdatedAt:       nil,
			},
			setupMocks: func(mockRepo *MockAppointmentTypeUpsertRepository, mockValidation *MockAppointmentTypeValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(nil)
				businessErr := core.NewBusinessError("Duplicate value")
				mockRepo.On("UpsertAppointmentType", mock.Anything, mock.AnythingOfType("*entity.AppointmentType")).Return(businessErr)
			},
			wantErr:        true,
			wantErrMessage: "Duplicate value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(MockAppointmentTypeUpsertRepository)
			mockValidationService := new(MockAppointmentTypeValidationService)

			tt.setupMocks(mockRepo, mockValidationService)

			uc := usecase.NewUpsertAppointmentTypeUsecase(mockRepo, mockValidationService)
			output, err := uc.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
				assert.Nil(t, output)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, output)
				if tt.expectedOutput != nil {
					assert.Equal(t, tt.expectedOutput.AppointmentType, output.AppointmentType)
					assert.Equal(t, tt.expectedOutput.Enabled, output.Enabled)
					if tt.expectedOutput.ID != "" {
						assert.Equal(t, tt.expectedOutput.ID, output.ID)
					} else {
						// For cases without ID, just verify it's a valid UUID
						_, err := core.NewIDFromString(output.ID)
						assert.NoError(t, err)
					}
				}
			}

			mockRepo.AssertExpectations(t)
			mockValidationService.AssertExpectations(t)
		})
	}
}
