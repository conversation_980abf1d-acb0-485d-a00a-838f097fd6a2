package usecase

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
)

type mockListAppointmentTypeRepo struct{}

func (m *mockListAppointmentTypeRepo) ListAppointmentTypes(ctx context.Context, enabled *bool, page, size int) ([]*entity.AppointmentType, int64, error) {
	created := time.Date(2025, 9, 3, 10, 0, 0, 0, time.UTC)
	updated := time.Date(2025, 9, 3, 10, 0, 0, 0, time.UTC)
	appointmentType, _ := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              "969c3e7d-6853-46f3-b433-2b3f11d9e76e",
		AppointmentType: "Test",
		Enabled:         true,
		CreatedAt:       &created,
		UpdatedAt:       &updated,
	})
	return []*entity.AppointmentType{appointmentType}, 1, nil
}
func (m *mockListAppointmentTypeRepo) UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error {
	return nil
}

func TestListAppointmentTypeUsecase_Execute(t *testing.T) {
	repo := &mockListAppointmentTypeRepo{}
	uc := NewListAppointmentTypeUsecase(repo)
	enabled := true
	output, total, err := uc.Execute(context.Background(), &enabled, 1, 10)
	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.NotNil(t, output.Data)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, "969c3e7d-6853-46f3-b433-2b3f11d9e76e", output.Data[0].UUID)
	assert.Equal(t, "Test", output.Data[0].AppointmentType)
	assert.True(t, output.Data[0].Enabled)
	assert.Equal(t, "2025-09-03T10:00:00Z", output.Data[0].CreatedAt)
	assert.Equal(t, "2025-09-03T10:00:00Z", output.Data[0].UpdatedAt)
}
