package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var getAppointmentTypeUsecaseName = "GetAppointmentTypeUsecase"

type getAppointmentTypeRepository interface {
	GetAppointmentType(ctx context.Context, id string) (*entity.AppointmentType, error)
}

type GetAppointmentTypeUsecase struct {
	repo getAppointmentTypeRepository
}

type GetAppointmentTypeOutputModel struct {
	UUID            string
	AppointmentType string
	Enabled         bool
	CreatedAt       string
	UpdatedAt       string
}

type GetAppointmentTypeOutput struct {
	Data *GetAppointmentTypeOutputModel
}

func NewGetAppointmentTypeUsecase(repo getAppointmentTypeRepository) *GetAppointmentTypeUsecase {
	return &GetAppointmentTypeUsecase{
		repo: repo,
	}
}

func (u *GetAppointmentTypeUsecase) Execute(ctx context.Context, id string) (*GetAppointmentTypeOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", getAppointmentTypeUsecaseName))
	logger.Debug("trying to execute")

	appointmentType, err := u.repo.GetAppointmentType(ctx, id)
	if err != nil {
		return nil, err
	}

	return &GetAppointmentTypeOutput{
		Data: &GetAppointmentTypeOutputModel{
			UUID:            appointmentType.ID(),
			AppointmentType: appointmentType.AppointmentType(),
			Enabled:         appointmentType.Enabled(),
			CreatedAt:       appointmentType.CreatedAt().Format("2006-01-02T15:04:05Z"),
			UpdatedAt:       appointmentType.UpdatedAt().Format("2006-01-02T15:04:05Z"),
		},
	}, nil
}