package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var listAppointmentTypeUsecaseName = "ListAppointmentTypeUsecase"

type listAppointmentTypeRepository interface {
	ListAppointmentTypes(ctx context.Context, enabled *bool, page, size int) ([]*entity.AppointmentType, int64, error)
}

type ListAppointmentTypeUsecase struct {
	repo listAppointmentTypeRepository
}

type ListAppointmentTypeOutputModel struct {
	UUID            string
	AppointmentType string
	Enabled         bool
	CreatedAt       string
	UpdatedAt       string
}

type ListAppointmentTypeOutput struct {
	Data []*ListAppointmentTypeOutputModel
}

func NewListAppointmentTypeUsecase(repo listAppointmentTypeRepository) *ListAppointmentTypeUsecase {
	return &ListAppointmentTypeUsecase{
		repo: repo,
	}
}

func (u *ListAppointmentTypeUsecase) Execute(ctx context.Context, enabled *bool, page, size int) (*ListAppointmentTypeOutput, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", listAppointmentTypeUsecaseName))
	logger.Debug("trying to execute")

	appointmentTypes, total, err := u.repo.ListAppointmentTypes(ctx, enabled, page, size)
	if err != nil {
		return nil, 0, err
	}

	var appointmentTypeModels []*ListAppointmentTypeOutputModel
	for _, appointmentType := range appointmentTypes {
		if appointmentType == nil {
			appointmentTypeModels = append(appointmentTypeModels, nil)
			continue
		}
		appointmentTypeModels = append(appointmentTypeModels, &ListAppointmentTypeOutputModel{
			UUID:            appointmentType.ID(),
			AppointmentType: appointmentType.AppointmentType(),
			Enabled:         appointmentType.Enabled(),
			CreatedAt:       appointmentType.CreatedAt().Format("2006-01-02T15:04:05Z"),
			UpdatedAt:       appointmentType.UpdatedAt().Format("2006-01-02T15:04:05Z"),
		})
	}

	return &ListAppointmentTypeOutput{
		Data: appointmentTypeModels,
	}, total, nil
}