package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestAppointmentTypeRepository_UpsertMethod(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAppointmentTypeRepo(db)

	assert.NotNil(t, repo)
}

func TestAppointmentTypeRepository_UpsertAppointmentType(t *testing.T) {
	validID := core.NewID()
	now := time.Now().UTC()

	// Create a test appointment type entity
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Consultation",
		Enabled:         true,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name            string
		appointmentType *entity.AppointmentType
		setupMock       func(sqlmock.Sqlmock)
		wantErr         bool
		wantErrMessage  string
	}{
		{
			name:            "Successful upsert - INSERT",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				// Expect upsert query
				mock.ExpectExec(`INSERT INTO public\.appointment_type.*ON CONFLICT`).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:            "Successful upsert - UPDATE",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return existing timestamp (older)
				olderTime := now.Add(-1 * time.Hour)
				mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&olderTime))

				// Expect upsert query
				mock.ExpectExec(`INSERT INTO public\.appointment_type.*ON CONFLICT`).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:            "Event ignored - older timestamp",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return newer timestamp (event should be ignored)
				newerTime := now.Add(1 * time.Hour)
				mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(newerTime))

				// No upsert query should be executed since event is ignored
			},
			wantErr: false,
		},
		{
			name:            "Database error on check",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to fail
				mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name:            "Name constraint violation",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				// Expect check query to return no rows (new record)
				mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				// Expect upsert query to fail with constraint violation
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "appointment_type_name_key"`)
				mock.ExpectExec(`INSERT INTO public\.appointment_type.*ON CONFLICT`).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "duplicate key value violates unique constraint",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewAppointmentTypeRepo(db)
			err := repo.UpsertAppointmentType(context.Background(), tt.appointmentType)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestAppointmentTypeRepository_UpsertAppointmentType_DisabledStatus(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a disabled appointment type entity
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Cancelled",
		Enabled:         false,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	// Expect check query to return no rows (new record)
	mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnError(sql.ErrNoRows)

	expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Cancelled", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAppointmentTypeRepo(db)
	err = repo.UpsertAppointmentType(context.Background(), appointmentType)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestAppointmentTypeRepository_UpsertAppointmentType_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create an appointment type entity for update
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Updated Follow-up",
		Enabled:         false,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	// Expect check query to return existing timestamp (older)
	olderTime := now.Add(-1 * time.Hour)
	mock.ExpectQuery(`SELECT updated_at FROM public\.appointment_type WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&olderTime))

	// Mock expects the upsert query with ON CONFLICT DO UPDATE
	expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Updated Follow-up", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAppointmentTypeRepo(db)
	err = repo.UpsertAppointmentType(context.Background(), appointmentType)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
