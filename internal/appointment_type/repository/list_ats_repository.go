package repository

import (
	"context"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AppointmentTypeRepo struct {
	db *sqlx.DB
}

func NewAppointmentTypeRepo(db *sqlx.DB) *AppointmentTypeRepo {
	return &AppointmentTypeRepo{db: db}
}

func (r *AppointmentTypeRepo) ListAppointmentTypes(ctx context.Context, enabled *bool, page, size int) ([]*entity.AppointmentType, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "ListAppointmentTypes"), vlog.F("action", "list appointment types"))

	var (
		query      string
		countQuery string
		rows       *sqlx.Rows
		err        error
	)

	if enabled != nil {
		query = `SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type WHERE enabled = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3`
		rows, err = r.db.QueryxContext(ctx, query, *enabled, size, (page-1)*size)
		countQuery = "SELECT COUNT(uuid) FROM appointment_type WHERE enabled = $1"
	} else {
		query = `SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type ORDER BY created_at DESC LIMIT $1 OFFSET $2`
		rows, err = r.db.QueryxContext(ctx, query, size, (page-1)*size)
		countQuery = "SELECT COUNT(uuid) FROM appointment_type"
	}

	if err != nil {
		logger.Error("failed to query appointment_types: " + err.Error())
		return nil, 0, err
	}
	defer rows.Close()

	var output []*entity.AppointmentType
	for rows.Next() {
		var idStr, appointmentTypeStr string
		var enabledBool bool
		var createdAtRaw, updatedAtRaw time.Time
		if err := rows.Scan(&idStr, &appointmentTypeStr, &enabledBool, &createdAtRaw, &updatedAtRaw); err != nil {
			logger.Error("failed to scan appointment_type row: " + err.Error())
			return nil, 0, err
		}
		input := &entity.NewAppointmentTypeInput{
			ID:              idStr,
			AppointmentType: appointmentTypeStr,
			Enabled:         enabledBool,
			CreatedAt:       &createdAtRaw,
			UpdatedAt:       &updatedAtRaw,
		}
		appointmentType, err := entity.NewAppointmentType(input)
		if err != nil {
			logger.Error("failed to construct entity from DB", vlog.F("error", err), vlog.F("db_id", idStr))
			return nil, 0, err
		}
		output = append(output, appointmentType)
	}

	var total int64
	if enabled != nil {
		if err := r.db.GetContext(ctx, &total, countQuery, *enabled); err != nil {
			logger.Error("appointmentTypeRepository.GetAll: failed to get total count", vlog.F("error", err))
			return nil, 0, err
		}
	} else {
		if err := r.db.GetContext(ctx, &total, countQuery); err != nil {
			logger.Error("appointmentTypeRepository.GetAll: failed to get total count", vlog.F("error", err))
			return nil, 0, err
		}
	}

	return output, total, nil
}
