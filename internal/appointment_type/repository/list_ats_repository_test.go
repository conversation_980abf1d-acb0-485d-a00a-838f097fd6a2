package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAppointmentTypeRepo_ListAppointmentTypes_WithEnabled(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAppointmentTypeRepo(db)
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	now := time.Now()
	rows := sqlmock.NewRows(columns).
		AddRow("84ce4d70-a442-412b-bf27-06f4544a8661", "Consultation", true, now, now)
	mock.ExpectQuery(`SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type WHERE enabled = \$1 ORDER BY created_at DESC LIMIT \$2 OFFSET \$3`).WithArgs(true, 10, 0).WillReturnRows(rows)
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type WHERE enabled = \$1`).WithArgs(true).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	enabled := true
	result, total, err := repo.ListAppointmentTypes(ctx, &enabled, 1, 10)
	require.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, result, 1)
	assert.Equal(t, "Consultation", result[0].AppointmentType())
}

func TestAppointmentTypeRepo_ListAppointmentTypes_WithoutEnabled(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAppointmentTypeRepo(db)
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	now := time.Now()
	rows := sqlmock.NewRows(columns).
		AddRow("84ce4d70-a442-412b-bf27-06f4544a8661", "Consultation", true, now, now).
		AddRow("4beed17b-a38a-4da1-8b26-94d2f1513001", "Follow-up", true, now, now)
	mock.ExpectQuery(`SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type ORDER BY created_at DESC LIMIT \$1 OFFSET \$2`).WithArgs(10, 0).WillReturnRows(rows)
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

	result, total, err := repo.ListAppointmentTypes(ctx, nil, 1, 10)
	require.NoError(t, err)
	assert.Equal(t, int64(2), total)
	assert.Len(t, result, 2)
	assert.Equal(t, "Consultation", result[0].AppointmentType())
	assert.Equal(t, "Follow-up", result[1].AppointmentType())
}
