package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
    db, mock, err := sqlmock.New()
    require.NoError(t, err)
    sqlxDB := sqlx.NewDb(db, "postgres")
    return sqlxDB, mock
}
func TestAppointmentTypeRepository_GetAppointmentType(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAppointmentTypeRepo(db)
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	apptTypeID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(apptTypeID, "Consultation", true, now, now)
	mock.ExpectQuery(`SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type WHERE uuid = \$1`).WithArgs(apptTypeID).WillReturnRows(row)

	apptType, err := repo.GetAppointmentType(ctx, apptTypeID)
	require.NoError(t, err)
	assert.NotNil(t, apptType)
	assert.Equal(t, apptTypeID, apptType.ID())
	assert.Equal(t, "Consultation", apptType.AppointmentType())
	assert.True(t, apptType.Enabled())
	assert.WithinDuration(t, now, apptType.CreatedAt(), time.Second)
	assert.WithinDuration(t, now, apptType.UpdatedAt(), time.Second)
}
