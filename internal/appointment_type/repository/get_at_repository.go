package repository

import (
	"context"
	"time"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *AppointmentTypeRepo) GetAppointmentType(ctx context.Context, id string) (*entity.AppointmentType, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetAppointmentType"), vlog.F("action", "get appointment type by id"))

	query := `SELECT uuid, appointment_type, enabled, created_at, updated_at FROM appointment_type WHERE uuid = $1`
	var uuid, appointmentTypeStr string
	var enabled bool
	var createdAt, updatedAt time.Time
	row := r.db.QueryRowxContext(ctx, query, id)
	if err := row.Scan(&uuid, &appointmentTypeStr, &enabled, &createdAt, &updatedAt); err != nil {
		logger.Error("failed to scan appointment_type row: " + err.Error())
		return nil, err
	}
	input := &entity.NewAppointmentTypeInput{
		ID:              uuid,
		AppointmentType: appointmentTypeStr,
		Enabled:         enabled,
		CreatedAt:       &createdAt,
		UpdatedAt:       &updatedAt,
	}
	appointmentType, err := entity.NewAppointmentType(input)
	if err != nil {
		logger.Error("failed to construct entity from DB", vlog.F("error", err), vlog.F("db_id", uuid))
		return nil, err
	}
	return appointmentType, nil
}
