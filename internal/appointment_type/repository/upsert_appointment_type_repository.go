package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAppointmentType inserts or updates an appointment type
func (r *AppointmentTypeRepo) UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "AppointmentTypeUpsertRepository"), vlog.F("method", "UpsertAppointmentType"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.appointment_type WHERE uuid = $1`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("uuid", appointmentType.ID()))
	err := r.db.QueryRowContext(ctx, checkQuery, appointmentType.ID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing appointment type", vlog.F("error", err))
		return fmt.Errorf("failed to check existing appointment type: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", appointmentType.UpdatedAt()),
			vlog.F("incoming_is_newer", appointmentType.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && appointmentType.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("appointment type event discarded - older than existing record",
			vlog.F("id", appointmentType.ID()),
			vlog.F("appointment_type", appointmentType.AppointmentType()),
			vlog.F("event_updated_at", appointmentType.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.appointment_type (uuid, appointment_type, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (uuid)
		DO UPDATE SET
			appointment_type = EXCLUDED.appointment_type,
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.appointment_type.updated_at
	`

	args := []interface{}{
		appointmentType.ID(),
		appointmentType.AppointmentType(),
		appointmentType.Enabled(),
		appointmentType.CreatedAt(),
		appointmentType.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)
	if err != nil {
		// Check if it's a name constraint violation
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr), vlog.F("appointment_type", appointmentType.AppointmentType()))
			return parsedErr
		}

		logger.Error("failed to upsert appointment type", vlog.F("error", err))
		return fmt.Errorf("failed to upsert appointment type: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("appointment type updated successfully",
			vlog.F("id", appointmentType.ID()),
			vlog.F("appointment_type", appointmentType.AppointmentType()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("appointment type inserted successfully",
			vlog.F("id", appointmentType.ID()),
			vlog.F("appointment_type", appointmentType.AppointmentType()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
