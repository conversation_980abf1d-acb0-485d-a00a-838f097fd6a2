package appointment_type

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/service"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	db                           *sqlx.DB
	logger                       vlog.Logger
	AppointmentTypeEventListener *event.AppointmentTypeEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertAppointmentTypeUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertAppointmentTypeInput) (*usecase.UpsertAppointmentTypeOutput, error) {
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration, logger vlog.Logger) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewAppointmentTypeValidationService()

	// Initialize repository for upsert operations
	appointmentTypeRepo := repository.NewAppointmentTypeRepo(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertAppointmentTypeUsecase(appointmentTypeRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	var eventListener *event.AppointmentTypeEventListener
	var err error

	// Make SQS setup optional for local/dev
	if cfg.AWS.SQSQueueURLAppointmentType != "" {
		eventListener, err = event.NewAppointmentTypeEventListener(cfg, adapter)
		if err != nil {
			return nil, err
		}
	}

	return &Factory{
		db:                           db,
		logger:                       logger,
		AppointmentTypeEventListener: eventListener,
	}, nil
}

func (f *Factory) getRepoOrPanic() *repository.AppointmentTypeRepo {
	if f == nil || f.db == nil || f.logger == nil {
		panic("Factory, db, or logger is nil")
	}
	return repository.NewAppointmentTypeRepo(f.db)
}

func (f *Factory) ListAppointmentTypeHandler() *transport.ListAppointmentTypesHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewListAppointmentTypeUsecase(repo)
	h := transport.NewListAppointmentTypesHandler(uc)
	return h
}

func (f *Factory) GetAppointmentTypeHandler() *transport.GetAppointmentTypeHandler {
	repo := f.getRepoOrPanic()
	uc := usecase.NewGetAppointmentTypeUsecase(repo)
	h := transport.NewGetAppointmentTypeHandler(uc)
	return h
}
