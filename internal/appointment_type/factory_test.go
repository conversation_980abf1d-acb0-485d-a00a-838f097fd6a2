package appointment_type

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	return sqlx.NewDb(db, "postgres"), mock
}

func TestFactory_ListAndGetAppointmentTypeHandler(t *testing.T) {
	db, _ := setupMockDB(t)
	cfg := config.Configuration{}
	logger := vlog.NewWithLevel("error")
	factory, err := NewFactory(db, cfg, logger)
	require.NoError(t, err)
	assert.NotNil(t, factory)

	hList := factory.ListAppointmentTypeHandler()
	assert.NotNil(t, hList)
	assert.IsType(t, &transport.ListAppointmentTypesHandler{}, hList)

	hGet := factory.GetAppointmentTypeHandler()
	assert.NotNil(t, hGet)
	assert.IsType(t, &transport.GetAppointmentTypeHandler{}, hGet)
}

func TestNewFactory(t *testing.T) {
	db, _ := setupMockDB(t)
	defer db.Close()
	logger := vlog.NewWithLevel("error")

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLAppointmentType: "https://sqs.us-east-1.amazonaws.com/123456789/appointment-type-queue",
			Region:                     "us-east-1",
			AccessKeyID:                "test-access-key",
			SecretAccessKey:            "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg, logger)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.AppointmentTypeEventListener)
}

func TestNewFactory_MissingQueueURL(t *testing.T) {
	db, _ := setupMockDB(t)
	defer db.Close()

	logger := vlog.NewWithLevel("error")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLAppointmentType: "", // Missing queue URL
			Region:                     "us-east-1",
			AccessKeyID:                "test-access-key",
			SecretAccessKey:            "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg, logger)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.Nil(t, factory.AppointmentTypeEventListener)
}

func TestFactory_getRepoOrPanic_panicsOnNilDeps(t *testing.T) {
	logger := vlog.NewWithLevel("error")
	db, _ := setupMockDB(t)

	t.Run("nil factory", func(t *testing.T) {
		var f *Factory
		assert.Panics(t, func() { f.getRepoOrPanic() })
	})
	t.Run("nil db", func(t *testing.T) {
		f := &Factory{db: nil, logger: logger}
		assert.Panics(t, func() { f.getRepoOrPanic() })
	})
	t.Run("nil logger", func(t *testing.T) {
		f := &Factory{db: db, logger: nil}
		assert.Panics(t, func() { f.getRepoOrPanic() })
	})
}
