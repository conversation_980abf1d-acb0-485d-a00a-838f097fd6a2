package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertFieldInput represents input for field upsert operations
type UpsertFieldInput struct {
	ID          *string
	Name        string
	Description *string
	Icon        *string
	Position    *int
	Enabled     bool
	AreaID      *string
	UpdatedAt   *time.Time
}

// UpsertFieldOutput represents output for field upsert operations
type UpsertFieldOutput struct {
	ID          string
	Name        string
	Description *string
	Icon        *string
	Position    *int
	Enabled     bool
	AreaID      *string
}

// UpsertFieldUsecase defines the interface for field upsert operations
type UpsertFieldUsecase interface {
	Execute(ctx context.Context, input *UpsertFieldInput) (*UpsertFieldOutput, error)
}

// upsertFieldUsecase implements UpsertFieldUsecase
type upsertFieldUsecase struct {
	fieldRepository   repository.FieldUpsertRepository
	validationService service.FieldValidationService
}

// NewUpsertFieldUsecase creates a new field upsert use case
func NewUpsertFieldUsecase(
	fieldRepository repository.FieldUpsertRepository,
	validationService service.FieldValidationService,
) UpsertFieldUsecase {
	return &upsertFieldUsecase{
		fieldRepository:   fieldRepository,
		validationService: validationService,
	}
}

// Execute performs the field upsert operation
func (uc *upsertFieldUsecase) Execute(ctx context.Context, input *UpsertFieldInput) (*UpsertFieldOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "UpsertFieldUsecase"))

	// Validate input
	if input == nil {
		return nil, fmt.Errorf("input is required")
	}

	if input.Name == "" {
		return nil, fmt.Errorf("field name is required")
	}

	// Validate ID format if provided
	if input.ID != nil && *input.ID != "" {
		_, err := core.NewIDFromString(*input.ID)
		if err != nil {
			return nil, fmt.Errorf("invalid UUID format: %w", err)
		}
	}

	// Generate ID if not provided
	fieldID := ""
	if input.ID != nil && *input.ID != "" {
		fieldID = *input.ID
	} else {
		fieldID = core.NewID().Value()
	}

	// Validate
	validationInput := &service.UpsertValidationInput{
		ID:          &fieldID,
		Name:        input.Name,
		Description: input.Description,
		Icon:        input.Icon,
		Position:    input.Position,
		Enabled:     input.Enabled,
		AreaID:      input.AreaID,
	}

	if err := uc.validationService.ValidateUpsertInput(ctx, validationInput); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return nil, err
	}

	// Create field entity
	fieldEntity, err := entity.NewField(&entity.NewFieldInput{
		ID:          fieldID,
		Name:        input.Name,
		Description: input.Description,
		Icon:        input.Icon,
		Position:    input.Position,
		Enabled:     input.Enabled,
		AreaID:      input.AreaID,
		UpdatedAt:   input.UpdatedAt,
	})
	if err != nil {
		logger.Error("failed to create field entity", vlog.F("error", err))
		return nil, fmt.Errorf("failed to create field entity: %w", err)
	}

	// Validate business rules
	if err := uc.validationService.ValidateBusinessRules(ctx, fieldEntity); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return nil, err
	}

	// upsert
	if err := uc.fieldRepository.UpsertField(ctx, fieldEntity); err != nil {
		// Check if it's a business error
		var businessErr *core.BusinessError
		if errors.As(err, &businessErr) {
			logger.Error("field constraint violation", vlog.F("error", err), vlog.F("field_name", fieldEntity.Name()))
			return nil, err
		}

		logger.Error("failed to upsert field", vlog.F("error", err))
		return nil, fmt.Errorf("database error occurred while upserting field: %w", err)
	}

	logger.Info("field upserted successfully", vlog.F("id", fieldEntity.ID()), vlog.F("name", fieldEntity.Name()))

	// Return output
	return &UpsertFieldOutput{
		ID:          fieldEntity.ID(),
		Name:        fieldEntity.Name(),
		Description: fieldEntity.Description(),
		Icon:        fieldEntity.Icon(),
		Position:    fieldEntity.Position(),
		Enabled:     fieldEntity.Enabled(),
		AreaID:      fieldEntity.AreaID(),
	}, nil
}
