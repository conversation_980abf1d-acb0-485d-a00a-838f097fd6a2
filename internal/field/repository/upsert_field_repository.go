package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// FieldUpsertRepository defines the interface for field upsert operations
type FieldUpsertRepository interface {
	UpsertField(ctx context.Context, field *entity.Field) error
}

// fieldUpsertRepository implements FieldUpsertRepository
type fieldUpsertRepository struct {
	db *sqlx.DB
}

// NewFieldUpsertRepository creates a new field upsert repository
func NewFieldUpsertRepository(db *sqlx.DB) FieldUpsertRepository {
	return &fieldUpsertRepository{db: db}
}

// UpsertField upserts a field entity into the database
func (r *fieldUpsertRepository) UpsertField(ctx context.Context, field *entity.Field) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "FieldRepository"), vlog.F("method", "UpsertField"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.field WHERE uuid = $1`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery), vlog.F("uuid", field.ID()))
	err := r.db.QueryRowContext(ctx, checkQuery, field.ID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing field", vlog.F("error", err))
		return fmt.Errorf("failed to check existing field: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", field.UpdatedAt()),
			vlog.F("incoming_is_newer", field.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && field.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("field event discarded - older than existing record",
			vlog.F("id", field.ID()),
			vlog.F("name", field.Name()),
			vlog.F("event_updated_at", field.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.field (uuid, name, description, icon, "position", enabled, area_uuid, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (uuid)
		DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			"position" = EXCLUDED."position",
			enabled = EXCLUDED.enabled,
			area_uuid = EXCLUDED.area_uuid,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.field.updated_at
	`

	args := []interface{}{
		field.ID(),
		field.Name(),
		field.Description(),
		field.Icon(),
		field.Position(),
		field.Enabled(),
		field.AreaID(),
		field.CreatedAt(),
		field.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)
	if err != nil {
		// Check if it's a name constraint violation
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr), vlog.F("field", field.Name()))
			return parsedErr
		}

		logger.Error("failed to upsert field", vlog.F("error", err))
		return fmt.Errorf("failed to upsert field: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("field updated successfully",
			vlog.F("id", field.ID()),
			vlog.F("name", field.Name()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("field inserted successfully",
			vlog.F("id", field.ID()),
			vlog.F("name", field.Name()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}
