package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewFieldUpsertRepository(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewFieldUpsertRepository(db)

	assert.NotNil(t, repo)
}

func TestFieldRepository_UpsertField(t *testing.T) {
	validID := core.NewID()
	validAreaID := core.NewID()
	now := time.Now().UTC()

	// Create a test field entity
	field, err := entity.NewField(&entity.NewFieldInput{
		ID:          validID.Value(),
		Name:        "Cardiology",
		Description: core.ToPtr("Heart-related medical specialty"),
		Icon:        core.ToPtr("heart-icon"),
		Position:    core.ToPtr(1),
		Enabled:     true,
		AreaID:      core.ToPtr(validAreaID.Value()),
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name           string
		field          *entity.Field
		setupMock      func(sqlmock.Sqlmock)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name:  "Successful upsert - INSERT",
			field: field,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(
						validID.Value(),
						"Cardiology",
						core.ToPtr("Heart-related medical specialty"),
						core.ToPtr("heart-icon"),
						core.ToPtr(1),
						true,
						core.ToPtr(validAreaID.Value()),
						now,
						now,
					).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:  "Database error",
			field: field,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(
						validID.Value(),
						"Cardiology",
						core.ToPtr("Heart-related medical specialty"),
						core.ToPtr("heart-icon"),
						core.ToPtr(1),
						true,
						core.ToPtr(validAreaID.Value()),
						now,
						now,
					).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name:  "Name constraint violation",
			field: field,
			setupMock: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
					WithArgs(validID.Value()).
					WillReturnError(sql.ErrNoRows)

				expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "field_name_key"`)
				mock.ExpectExec(expectedQuery).
					WithArgs(
						validID.Value(),
						"Cardiology",
						core.ToPtr("Heart-related medical specialty"),
						core.ToPtr("heart-icon"),
						core.ToPtr(1),
						true,
						core.ToPtr(validAreaID.Value()),
						now,
						now,
					).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "field_name_key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewFieldUpsertRepository(db)
			err := repo.UpsertField(context.Background(), tt.field)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestFieldRepository_UpsertField_MinimalFields(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a minimal field entity
	field, err := entity.NewField(&entity.NewFieldInput{
		ID:        validID.Value(),
		Name:      "General Practice",
		Enabled:   true,
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnError(sql.ErrNoRows)

	expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(
			validID.Value(),
			"General Practice",
			(*string)(nil), // description
			(*string)(nil), // icon
			(*int)(nil),    // position
			true,
			(*string)(nil), // area_id
			now,
			now,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewFieldUpsertRepository(db)
	err = repo.UpsertField(context.Background(), field)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestFieldRepository_UpsertField_DisabledField(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a disabled field entity
	field, err := entity.NewField(&entity.NewFieldInput{
		ID:          validID.Value(),
		Name:        "Obsolete Field",
		Description: core.ToPtr("No longer used"),
		Enabled:     false,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnError(sql.ErrNoRows)

	expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(
			validID.Value(),
			"Obsolete Field",
			core.ToPtr("No longer used"),
			(*string)(nil), // icon
			(*int)(nil),    // position
			false,
			(*string)(nil), // area_id
			now,
			now,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewFieldUpsertRepository(db)
	err = repo.UpsertField(context.Background(), field)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestFieldRepository_UpsertField_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	validAreaID := core.NewID()
	now := time.Now().UTC()

	// Create a field entity for update
	field, err := entity.NewField(&entity.NewFieldInput{
		ID:          validID.Value(),
		Name:        "Updated Neurology",
		Description: core.ToPtr("Updated brain and nervous system specialty"),
		Icon:        core.ToPtr("updated-brain-icon"),
		Position:    core.ToPtr(10),
		Enabled:     false,
		AreaID:      core.ToPtr(validAreaID.Value()),
		CreatedAt:   &now,
		UpdatedAt:   &now,
	})
	require.NoError(t, err)

	olderTime := now.Add(-1 * time.Hour)
	mock.ExpectQuery(`SELECT updated_at FROM public\.field WHERE uuid`).
		WithArgs(validID.Value()).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&olderTime))

	expectedQuery := `INSERT INTO public\.field.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(
			validID.Value(),
			"Updated Neurology",
			core.ToPtr("Updated brain and nervous system specialty"),
			core.ToPtr("updated-brain-icon"),
			core.ToPtr(10),
			false,
			core.ToPtr(validAreaID.Value()),
			now,
			now,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewFieldUpsertRepository(db)
	err = repo.UpsertField(context.Background(), field)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
