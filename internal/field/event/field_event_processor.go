package event

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertFieldUsecase defines the interface for field upsert operations
type UpsertFieldUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertFieldInput) (*usecase.UpsertFieldOutput, error)
}

// FieldUpsertEventProcessor processes field upsert events
type FieldUpsertEventProcessor struct {
	upsertUsecase UpsertFieldUsecase
}

// NewFieldUpsertEventProcessor creates a new field upsert event processor
func NewFieldUpsertEventProcessor(upsertUsecase UpsertFieldUsecase) *FieldUpsertEventProcessor {
	return &FieldUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// IsBusinessError determines if an error is a business logic error that should go to DLQ
func (p *FieldUpsertEventProcessor) IsBusinessError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Check for constraint violations (business errors)
	businessErrorPatterns := []string{
		"field name",
		"already exists",
		"constraint violation",
		"duplicate key value",
		"field_name_key",
	}

	for _, pattern := range businessErrorPatterns {
		if strings.Contains(strings.ToLower(errorMsg), strings.ToLower(pattern)) {
			return true
		}
	}

	// Check for core business errors
	var businessErr *core.BusinessError
	if errors.As(err, &businessErr) {
		return true
	}

	return false
}

// ProcessMessage processes a field event message
func (p *FieldUpsertEventProcessor) ProcessMessage(ctx context.Context, message *FieldEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "FieldUpsertEventProcessor"))
	logger.Debug("handling field event message")

	// Validate message data
	if message.Name == "" {
		logger.Error("validation failed", vlog.F("error", "field name is required"))
		return core.NewBusinessError("field name is required in message data")
	}

	// Validate
	if message.ID != nil && *message.ID != "" {
		_, err := core.NewIDFromString(*message.ID)
		if err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.ID))
			return core.NewBusinessError("invalid UUID format in message data")
		}
	}

	if message.AreaID != nil && *message.AreaID != "" {
		_, err := core.NewIDFromString(*message.AreaID)
		if err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid area UUID format"), vlog.F("area_id", *message.AreaID))
			return core.NewBusinessError("invalid area UUID format in message data")
		}
	}

	upsertInput := &usecase.UpsertFieldInput{
		ID:          message.ID,
		Name:        message.Name,
		Description: message.Description,
		Icon:        message.Icon,
		Position:    message.Position,
		Enabled:     message.Enabled,
		AreaID:      message.AreaID,
		UpdatedAt:   message.UpdatedAt,
	}

	logger.Info("processing field upsert",
		vlog.F("name", message.Name),
		vlog.F("enabled", message.Enabled),
		vlog.F("has_id", message.ID != nil),
		vlog.F("has_area_id", message.AreaID != nil))

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		// Check if this is a business error that should go to DLQ
		if p.IsBusinessError(err) {
			logger.Error("business error occurred - message should go to DLQ",
				vlog.F("error", err),
				vlog.F("field", message.Name),
				vlog.F("error_type", "BUSINESS_ERROR"),
				vlog.F("dlq_candidate", true))

			return core.NewBusinessError("Business error processing field '%s': %v", message.Name, err)
		}

		// This is a transient error - should be retried
		logger.Error("transient error occurred - message will be retried",
			vlog.F("error", err),
			vlog.F("field", message.Name),
			vlog.F("error_type", "TRANSIENT_ERROR"),
			vlog.F("retryable", true))

		return fmt.Errorf("transient error processing field upsert: %w", err)
	}

	logger.Info("successfully processed field upsert", vlog.F("name", message.Name))
	return nil
}
