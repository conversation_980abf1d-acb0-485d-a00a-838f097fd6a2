package event

import (
	"time"
)

// FieldEventMessage represents the event message structure for field operations
type FieldEventMessage struct {
	ID          *string    `json:"id,omitempty"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	Icon        *string    `json:"icon,omitempty"`
	Position    *int       `json:"position,omitempty"`
	Enabled     bool       `json:"enabled"`
	AreaID      *string    `json:"area_id,omitempty"`
	CreatedAt   *time.Time `json:"created_at,omitempty"`
	UpdatedAt   *time.Time `json:"updated_at,omitempty"`
}
