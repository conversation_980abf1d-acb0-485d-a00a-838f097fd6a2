package event_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/event"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
)

// MockUpsertFieldUsecase is a mock implementation of the UpsertFieldUsecase interface
type MockUpsertFieldUsecase struct {
	mock.Mock
}

func (m *MockUpsertFieldUsecase) Execute(ctx context.Context, input *usecase.UpsertFieldInput) (*usecase.UpsertFieldOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertFieldOutput), args.Error(1)
}

func TestNewFieldUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(MockUpsertFieldUsecase)
	processor := event.NewFieldUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
	assert.IsType(t, &event.FieldUpsertEventProcessor{}, processor)
}

func TestFieldUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()
	validAreaID := core.NewID()

	tests := []struct {
		name           string
		message        *event.FieldEventMessage
		setupMock      func(*MockUpsertFieldUsecase)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with ID and area",
			message: &event.FieldEventMessage{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: core.ToPtr("Heart-related medical specialty"),
				Icon:        core.ToPtr("heart-icon"),
				Position:    core.ToPtr(1),
				Enabled:     true,
				AreaID:      core.ToPtr(validAreaID.Value()),
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					ID:          core.ToPtr(validID.Value()),
					Name:        "Cardiology",
					Description: core.ToPtr("Heart-related medical specialty"),
					Icon:        core.ToPtr("heart-icon"),
					Position:    core.ToPtr(1),
					Enabled:     true,
					AreaID:      core.ToPtr(validAreaID.Value()),
					UpdatedAt:   nil,
				}
				expectedOutput := &usecase.UpsertFieldOutput{
					ID:          validID.Value(),
					Name:        "Cardiology",
					Description: core.ToPtr("Heart-related medical specialty"),
					Icon:        core.ToPtr("heart-icon"),
					Position:    core.ToPtr(1),
					Enabled:     true,
					AreaID:      core.ToPtr(validAreaID.Value()),
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing without ID",
			message: &event.FieldEventMessage{
				Name:    "General Practice",
				Enabled: true,
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					ID:        nil,
					Name:      "General Practice",
					Enabled:   true,
					UpdatedAt: nil,
				}
				expectedOutput := &usecase.UpsertFieldOutput{
					ID:      "new-generated-id",
					Name:    "General Practice",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing with minimal fields",
			message: &event.FieldEventMessage{
				Name:    "Emergency Medicine",
				Enabled: true,
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					Name:      "Emergency Medicine",
					Enabled:   true,
					UpdatedAt: nil,
				}
				expectedOutput := &usecase.UpsertFieldOutput{
					ID:      "generated-id",
					Name:    "Emergency Medicine",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name:           "Nil message",
			message:        nil,
			setupMock:      func(mockUsecase *MockUpsertFieldUsecase) {},
			wantErr:        true,
			wantErrMessage: "message is required",
		},
		{
			name: "Empty field name",
			message: &event.FieldEventMessage{
				Name:    "",
				Enabled: true,
			},
			setupMock:      func(mockUsecase *MockUpsertFieldUsecase) {},
			wantErr:        true,
			wantErrMessage: "field name is required in message data",
		},
		{
			name: "Invalid UUID format",
			message: &event.FieldEventMessage{
				ID:      core.ToPtr("invalid-uuid"),
				Name:    "Test Field",
				Enabled: true,
			},
			setupMock:      func(mockUsecase *MockUpsertFieldUsecase) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format in message data",
		},
		{
			name: "Invalid area UUID format",
			message: &event.FieldEventMessage{
				Name:    "Test Field",
				Enabled: true,
				AreaID:  core.ToPtr("invalid-area-uuid"),
			},
			setupMock:      func(mockUsecase *MockUpsertFieldUsecase) {},
			wantErr:        true,
			wantErrMessage: "invalid area UUID format in message data",
		},
		{
			name: "Usecase execution error",
			message: &event.FieldEventMessage{
				Name:    "Test Field",
				Enabled: true,
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					Name:      "Test Field",
					Enabled:   true,
					UpdatedAt: nil,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "transient error processing field upsert",
		},
		{
			name: "Business error from usecase",
			message: &event.FieldEventMessage{
				Name:    "duplicate-name",
				Enabled: true,
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					Name:      "duplicate-name",
					Enabled:   true,
					UpdatedAt: nil,
				}
				businessErr := core.NewBusinessError("Field name 'duplicate-name' already exists")
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, businessErr)
			},
			wantErr:        true,
			wantErrMessage: "Business error processing field 'duplicate-name'",
		},
		{
			name: "Successful processing with disabled status",
			message: &event.FieldEventMessage{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Obsolete Field",
				Enabled: false,
			},
			setupMock: func(mockUsecase *MockUpsertFieldUsecase) {
				expectedInput := &usecase.UpsertFieldInput{
					ID:        core.ToPtr(validID.Value()),
					Name:      "Obsolete Field",
					Enabled:   false,
					UpdatedAt: nil,
				}
				expectedOutput := &usecase.UpsertFieldOutput{
					ID:      validID.Value(),
					Name:    "Obsolete Field",
					Enabled: false,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUsecase := new(MockUpsertFieldUsecase)
			tt.setupMock(mockUsecase)

			processor := event.NewFieldUpsertEventProcessor(mockUsecase)
			err := processor.ProcessMessage(context.Background(), tt.message)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			mockUsecase.AssertExpectations(t)
		})
	}
}

func TestFieldUpsertEventProcessor_IsBusinessError(t *testing.T) {
	processor := event.NewFieldUpsertEventProcessor(nil)

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "Nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "Core business error",
			err:      core.NewBusinessError("Duplicate value"),
			expected: true,
		},
		{
			name:     "Field name constraint error",
			err:      errors.New("field name already exists"),
			expected: true,
		},
		{
			name:     "Constraint violation error",
			err:      errors.New("constraint violation occurred"),
			expected: true,
		},
		{
			name:     "Duplicate key error",
			err:      errors.New("duplicate key value violates constraint"),
			expected: true,
		},
		{
			name:     "Field name key error",
			err:      errors.New("field_name_key constraint failed"),
			expected: true,
		},
		{
			name:     "Generic database error",
			err:      errors.New("connection timeout"),
			expected: false,
		},
		{
			name:     "Network error",
			err:      errors.New("network unreachable"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.IsBusinessError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}
