package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type TeamMemberPriceListRepo interface {
	UpsertBranchTeamMemberField(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error
	SoftDeleteBranchTeamMemberFields(ctx context.Context, branchID, accountID string) error
}

type teamMemberPriceListRepo struct {
	db *sqlx.DB
}

// NewTeamMemberPriceListRepo creates a new team member price list repository
func NewTeamMemberPriceListRepo(db *sqlx.DB) TeamMemberPriceListRepo {
	return &teamMemberPriceListRepo{db: db}
}

// UpsertBranchTeamMemberField upserts a branch team member field entity into the database
func (r *teamMemberPriceListRepo) UpsertBranchTeamMemberField(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "TeamMemberPriceListRepository"), vlog.F("method", "UpsertBranchTeamMemberField"))

	// First, try to get the existing record to check timestamp
	checkQuery := `SELECT updated_at FROM public.branch_team_member_fields WHERE branch_id = $1 AND account_id = $2 AND field_id = $3 AND field_status_id = $4`
	var existingUpdatedAt *time.Time

	logger.Debug("executing check query", vlog.F("query", checkQuery),
		vlog.F("branch_id", branchTeamMemberField.BranchID()),
		vlog.F("account_id", branchTeamMemberField.AccountID()),
		vlog.F("field_id", branchTeamMemberField.FieldID()),
		vlog.F("field_status_id", branchTeamMemberField.FieldStatusID()))

	err := r.db.QueryRowContext(ctx, checkQuery,
		branchTeamMemberField.BranchID(),
		branchTeamMemberField.AccountID(),
		branchTeamMemberField.FieldID(),
		branchTeamMemberField.FieldStatusID()).Scan(&existingUpdatedAt)

	if err != nil && !core.IsErrNoItemFound(err) {
		logger.Error("failed to check existing branch team member field", vlog.F("error", err))
		return fmt.Errorf("failed to check existing branch team member field: %w", err)
	}

	if existingUpdatedAt != nil {
		logger.Debug("found existing record",
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("incoming_updated_at", branchTeamMemberField.UpdatedAt()),
			vlog.F("incoming_is_newer", branchTeamMemberField.UpdatedAt().After(*existingUpdatedAt)))
	} else {
		logger.Debug("no existing record found - will INSERT")
	}

	// If record exists and incoming timestamp is older, ignore the event
	if existingUpdatedAt != nil && branchTeamMemberField.UpdatedAt().Before(*existingUpdatedAt) {
		logger.Info("branch team member field event discarded - older than existing record",
			vlog.F("branch_id", branchTeamMemberField.BranchID()),
			vlog.F("account_id", branchTeamMemberField.AccountID()),
			vlog.F("field_id", branchTeamMemberField.FieldID()),
			vlog.F("field_status_id", branchTeamMemberField.FieldStatusID()),
			vlog.F("event_updated_at", branchTeamMemberField.UpdatedAt()),
			vlog.F("existing_updated_at", *existingUpdatedAt),
			vlog.F("operation", "IGNORED"))
		return nil
	}

	// Perform the upsert
	upsertQuery := `
		INSERT INTO public.branch_team_member_fields (id, branch_id, account_id, field_id, field_status_id, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		ON CONFLICT (branch_id, account_id, field_id, field_status_id)
		DO UPDATE SET
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
		WHERE EXCLUDED.updated_at > public.branch_team_member_fields.updated_at`

	args := []interface{}{
		branchTeamMemberField.ID(),
		branchTeamMemberField.BranchID(),
		branchTeamMemberField.AccountID(),
		branchTeamMemberField.FieldID(),
		branchTeamMemberField.FieldStatusID(),
		branchTeamMemberField.Enabled(),
		branchTeamMemberField.CreatedAt(),
		branchTeamMemberField.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", upsertQuery), vlog.F("args", args))

	_, err = r.db.ExecContext(ctx, upsertQuery, args...)

	if err != nil {
		// Check for constraint violations
		if parsedErr := core.ParseDBError(err); parsedErr != nil {
			logger.Error("database constraint violation", vlog.F("error", parsedErr),
				vlog.F("branch_id", branchTeamMemberField.BranchID()),
				vlog.F("account_id", branchTeamMemberField.AccountID()),
				vlog.F("field_id", branchTeamMemberField.FieldID()),
				vlog.F("field_status_id", branchTeamMemberField.FieldStatusID()))
			return parsedErr
		}

		logger.Error("failed to upsert branch team member field", vlog.F("error", err))
		return fmt.Errorf("failed to upsert branch team member field: %w", err)
	}

	// Determine operation type based on whether record existed
	if existingUpdatedAt != nil {
		logger.Info("branch team member field updated successfully",
			vlog.F("branch_id", branchTeamMemberField.BranchID()),
			vlog.F("account_id", branchTeamMemberField.AccountID()),
			vlog.F("field_id", branchTeamMemberField.FieldID()),
			vlog.F("field_status_id", branchTeamMemberField.FieldStatusID()),
			vlog.F("enabled", branchTeamMemberField.Enabled()),
			vlog.F("operation", "UPDATE"))
	} else {
		logger.Info("branch team member field inserted successfully",
			vlog.F("branch_id", branchTeamMemberField.BranchID()),
			vlog.F("account_id", branchTeamMemberField.AccountID()),
			vlog.F("field_id", branchTeamMemberField.FieldID()),
			vlog.F("field_status_id", branchTeamMemberField.FieldStatusID()),
			vlog.F("enabled", branchTeamMemberField.Enabled()),
			vlog.F("operation", "INSERT"))
	}

	return nil
}

// SoftDeleteBranchTeamMemberFields soft deletes branch team member fields by setting enabled = false
func (r *teamMemberPriceListRepo) SoftDeleteBranchTeamMemberFields(ctx context.Context, branchID, accountID string) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "TeamMemberPriceListRepository"), vlog.F("method", "SoftDeleteBranchTeamMemberFields"))

	updateQuery := `UPDATE public.branch_team_member_fields SET enabled = false, updated_at = now() WHERE branch_id = $1 AND account_id = $2 AND enabled = true`

	result, err := r.db.ExecContext(ctx, updateQuery, branchID, accountID)
	if err != nil {
		logger.Error("failed to soft delete branch team member fields", vlog.F("error", err))
		return core.NewBusinessError("database error occurred while soft deleting branch team member fields: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("failed to get rows affected for branch team member fields deletion", vlog.F("error", err))
		return core.NewBusinessError("database error occurred while checking deletion result: %v", err)
	}

	logger.Info("branch team member fields soft deleted successfully",
		vlog.F("branch_id", branchID),
		vlog.F("account_id", accountID),
		vlog.F("rows_affected", rowsAffected))

	return nil
}
