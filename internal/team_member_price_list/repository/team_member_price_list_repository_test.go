package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
)

func TestUpsertBranchTeamMemberField(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewTeamMemberPriceListRepo(sqlxDB)

	ctx := context.Background()
	now := time.Now().UTC()

	field, err := entity.NewBranchTeamMemberField(&entity.NewBranchTeamMemberFieldInput{
		BranchID:      "550e8400-e29b-41d4-a716-************",
		AccountID:     "550e8400-e29b-41d4-a716-************",
		FieldID:       "550e8400-e29b-41d4-a716-************",
		FieldStatusID: "550e8400-e29b-41d4-a716-************",
		Enabled:       true,
		CreatedAt:     &now,
		UpdatedAt:     &now,
	})
	require.NoError(t, err)

	t.Run("Successful upsert - INSERT", func(t *testing.T) {
		mock.ExpectQuery(`SELECT updated_at FROM public\.branch_team_member_fields WHERE branch_id`).
			WithArgs(field.BranchID(), field.AccountID(), field.FieldID(), field.FieldStatusID()).
			WillReturnError(sql.ErrNoRows)

		mock.ExpectExec("INSERT INTO public.branch_team_member_fields").
			WithArgs(
				field.ID(),
				field.BranchID(),
				field.AccountID(),
				field.FieldID(),
				field.FieldStatusID(),
				field.Enabled(),
				field.CreatedAt(),
				field.UpdatedAt(),
			).
			WillReturnResult(sqlmock.NewResult(1, 1))

		err := repo.UpsertBranchTeamMemberField(ctx, field)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Database error", func(t *testing.T) {
		mock.ExpectQuery(`SELECT updated_at FROM public\.branch_team_member_fields WHERE branch_id`).
			WithArgs(field.BranchID(), field.AccountID(), field.FieldID(), field.FieldStatusID()).
			WillReturnError(sql.ErrNoRows)

		mock.ExpectExec("INSERT INTO public.branch_team_member_fields").
			WithArgs(
				field.ID(),
				field.BranchID(),
				field.AccountID(),
				field.FieldID(),
				field.FieldStatusID(),
				field.Enabled(),
				field.CreatedAt(),
				field.UpdatedAt(),
			).
			WillReturnError(assert.AnError)

		err := repo.UpsertBranchTeamMemberField(ctx, field)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Older event ignored", func(t *testing.T) {
		// Create a field with older timestamp
		olderTime := now.Add(-1 * time.Hour)
		olderField, err := entity.NewBranchTeamMemberField(&entity.NewBranchTeamMemberFieldInput{
			BranchID:      "550e8400-e29b-41d4-a716-************",
			AccountID:     "550e8400-e29b-41d4-a716-************",
			FieldID:       "550e8400-e29b-41d4-a716-************",
			FieldStatusID: "550e8400-e29b-41d4-a716-************",
			Enabled:       true,
			CreatedAt:     &olderTime,
			UpdatedAt:     &olderTime,
		})
		require.NoError(t, err)

		mock.ExpectQuery(`SELECT updated_at FROM public\.branch_team_member_fields WHERE branch_id`).
			WithArgs(olderField.BranchID(), olderField.AccountID(), olderField.FieldID(), olderField.FieldStatusID()).
			WillReturnRows(sqlmock.NewRows([]string{"updated_at"}).AddRow(&now))

		err = repo.UpsertBranchTeamMemberField(ctx, olderField)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestSoftDeleteBranchTeamMemberFields(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "sqlmock")
	repo := NewTeamMemberPriceListRepo(sqlxDB)

	ctx := context.Background()
	branchID := "550e8400-e29b-41d4-a716-************"
	accountID := "550e8400-e29b-41d4-a716-************"

	t.Run("Successful soft delete", func(t *testing.T) {
		mock.ExpectExec("UPDATE public.branch_team_member_fields SET enabled = false, updated_at = now()").
			WithArgs(branchID, accountID).
			WillReturnResult(sqlmock.NewResult(0, 2))

		err := repo.SoftDeleteBranchTeamMemberFields(ctx, branchID, accountID)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Database error", func(t *testing.T) {
		mock.ExpectExec("UPDATE public.branch_team_member_fields SET enabled = false, updated_at = now()").
			WithArgs(branchID, accountID).
			WillReturnError(assert.AnError)

		err := repo.SoftDeleteBranchTeamMemberFields(ctx, branchID, accountID)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
