package team_member_price_list

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/event"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/service"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/usecase"
)

// Factory contains all the dependencies for the team member price list module
type Factory struct {
	TeamMemberPriceListValidationService   service.TeamMemberPriceListValidationService
	TeamMemberPriceListRepo                repository.TeamMemberPriceListRepo
	ProcessTeamMemberPriceListEventUsecase usecase.ProcessTeamMemberPriceListEventUsecase
	TeamMemberPriceListEventListener       *event.TeamMemberPriceListEventListener
}

// NewFactory creates a new factory with all dependencies
func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Create validation service
	teamMemberPriceListValidationService := service.NewTeamMemberPriceListValidationService()

	// Create repository
	teamMemberPriceListRepo := repository.NewTeamMemberPriceListRepo(db)

	// Create usecase
	processTeamMemberPriceListEventUsecase := usecase.NewProcessTeamMemberPriceListEventUsecase(
		teamMemberPriceListRepo,
		teamMemberPriceListValidationService,
	)

	// Create event listener
	teamMemberPriceListEventListener, err := event.NewTeamMemberPriceListEventListener(
		cfg,
		processTeamMemberPriceListEventUsecase,
	)
	if err != nil {
		return nil, err
	}

	return &Factory{
		TeamMemberPriceListValidationService:   teamMemberPriceListValidationService,
		TeamMemberPriceListRepo:                teamMemberPriceListRepo,
		ProcessTeamMemberPriceListEventUsecase: processTeamMemberPriceListEventUsecase,
		TeamMemberPriceListEventListener:       teamMemberPriceListEventListener,
	}, nil
}
