package usecase

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/service"
)

// Mock repository
type MockTeamMemberPriceListRepo struct {
	mock.Mock
}

func (m *MockTeamMemberPriceListRepo) UpsertBranchTeamMemberField(ctx context.Context, field *entity.BranchTeamMemberField) error {
	args := m.Called(ctx, field)
	return args.Error(0)
}

func (m *MockTeamMemberPriceListRepo) SoftDeleteBranchTeamMemberFields(ctx context.Context, branchID, accountID string) error {
	args := m.Called(ctx, branchID, accountID)
	return args.Error(0)
}

func (m *MockTeamMemberPriceListRepo) GetBranchTeamMemberFieldsByBranch(ctx context.Context, branchID string) ([]*entity.BranchTeamMemberField, error) {
	args := m.Called(ctx, branchID)
	return args.Get(0).([]*entity.BranchTeamMemberField), args.Error(1)
}

func (m *MockTeamMemberPriceListRepo) GetBranchTeamMemberFieldsByBranchAndAccount(ctx context.Context, branchID, accountID string) ([]*entity.BranchTeamMemberField, error) {
	args := m.Called(ctx, branchID, accountID)
	return args.Get(0).([]*entity.BranchTeamMemberField), args.Error(1)
}

// Mock validation service
type MockTeamMemberPriceListValidationService struct {
	mock.Mock
}

func (m *MockTeamMemberPriceListValidationService) ValidateTeamMemberAddedInput(ctx context.Context, input *service.TeamMemberAddedValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockTeamMemberPriceListValidationService) ValidateTeamMemberRemovedInput(ctx context.Context, input *service.TeamMemberRemovedValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockTeamMemberPriceListValidationService) ValidateBranchTeamMemberFieldBusinessRules(ctx context.Context, field *entity.BranchTeamMemberField) error {
	args := m.Called(ctx, field)
	return args.Error(0)
}

func TestExecute_TeamMemberAdded(t *testing.T) {
	ctx := context.Background()
	now := time.Now().UTC()

	eventMessage := &TeamMemberPriceListEventMessage{
		BranchID:  "550e8400-e29b-41d4-a716-************",
		AccountID: "550e8400-e29b-41d4-a716-************",
		Role: TeamMemberPriceListRoleData{
			ID: "550e8400-e29b-41d4-a716-************",
		},
		Fields: []TeamMemberPriceListFieldData{
			{
				ID:       "550e8400-e29b-41d4-a716-************",
				StatusID: "550e8400-e29b-41d4-a716-************",
			},
		},
		UpdatedAt: now,
	}

	messageBody, err := json.Marshal(eventMessage)
	require.NoError(t, err)

	input := &ProcessTeamMemberPriceListEventInput{
		MessageBody: string(messageBody),
		EventType:   "team-member-added-price-list",
	}

	t.Run("Successful processing", func(t *testing.T) {
		mockRepo := &MockTeamMemberPriceListRepo{}
		mockValidationService := &MockTeamMemberPriceListValidationService{}
		usecase := NewProcessTeamMemberPriceListEventUsecase(mockRepo, mockValidationService)

		mockValidationService.On("ValidateTeamMemberAddedInput", ctx, mock.AnythingOfType("*service.TeamMemberAddedValidationInput")).Return(nil)
		mockValidationService.On("ValidateBranchTeamMemberFieldBusinessRules", ctx, mock.AnythingOfType("*entity.BranchTeamMemberField")).Return(nil)
		mockRepo.On("UpsertBranchTeamMemberField", ctx, mock.AnythingOfType("*entity.BranchTeamMemberField")).Return(nil)

		output, err := usecase.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "TEAM_MEMBER_ADDED", output.Operation)

		mockRepo.AssertExpectations(t)
		mockValidationService.AssertExpectations(t)
	})

	t.Run("Validation error", func(t *testing.T) {
		mockRepo := &MockTeamMemberPriceListRepo{}
		mockValidationService := &MockTeamMemberPriceListValidationService{}
		usecase := NewProcessTeamMemberPriceListEventUsecase(mockRepo, mockValidationService)

		mockValidationService.On("ValidateTeamMemberAddedInput", ctx, mock.AnythingOfType("*service.TeamMemberAddedValidationInput")).Return(core.NewBusinessError("validation failed"))

		output, err := usecase.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)

		mockValidationService.AssertExpectations(t)
	})
}

func TestExecute_TeamMemberRemoved(t *testing.T) {
	mockRepo := &MockTeamMemberPriceListRepo{}
	mockValidationService := &MockTeamMemberPriceListValidationService{}

	usecase := NewProcessTeamMemberPriceListEventUsecase(mockRepo, mockValidationService)
	ctx := context.Background()
	now := time.Now().UTC()

	eventMessage := &TeamMemberPriceListEventMessage{
		BranchID:  "550e8400-e29b-41d4-a716-************",
		AccountID: "550e8400-e29b-41d4-a716-************",
		UpdatedAt: now,
	}

	messageBody, err := json.Marshal(eventMessage)
	require.NoError(t, err)

	input := &ProcessTeamMemberPriceListEventInput{
		MessageBody: string(messageBody),
		EventType:   "team-member-removed-price-list",
	}

	t.Run("Successful processing", func(t *testing.T) {
		mockValidationService.On("ValidateTeamMemberRemovedInput", ctx, mock.AnythingOfType("*service.TeamMemberRemovedValidationInput")).Return(nil)
		mockRepo.On("SoftDeleteBranchTeamMemberFields", ctx, eventMessage.BranchID, eventMessage.AccountID).Return(nil)

		output, err := usecase.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "TEAM_MEMBER_REMOVED", output.Operation)

		mockRepo.AssertExpectations(t)
		mockValidationService.AssertExpectations(t)
	})

	t.Run("Invalid event type", func(t *testing.T) {
		invalidInput := &ProcessTeamMemberPriceListEventInput{
			MessageBody: string(messageBody),
			EventType:   "invalid_event",
		}

		output, err := usecase.Execute(ctx, invalidInput)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Contains(t, err.Error(), "unsupported event type")
	})
}
