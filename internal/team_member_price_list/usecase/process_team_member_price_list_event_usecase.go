package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// TeamMemberPriceListRoleData represents the role information in the event
type TeamMemberPriceListRoleData struct {
	ID      string `json:"id"`
	Acronym string `json:"acronym"`
}

// TeamMemberPriceListFieldData represents the field information in the event
type TeamMemberPriceListFieldData struct {
	ID       string `json:"id"`
	StatusID string `json:"status_id"`
}

// TeamMemberPriceListEventMessage represents the event message structure for team member price list operations
type TeamMemberPriceListEventMessage struct {
	BranchID  string                         `json:"branch_id"`
	AccountID string                         `json:"account_id"`
	Role      TeamMemberPriceListRoleData    `json:"role"`
	Fields    []TeamMemberPriceListFieldData `json:"fields"`
	UpdatedAt time.Time                      `json:"updated_at"`
}

// ProcessTeamMemberPriceListEventUsecase defines the interface for processing team member price list events
type ProcessTeamMemberPriceListEventUsecase interface {
	Execute(ctx context.Context, input *ProcessTeamMemberPriceListEventInput) (*ProcessTeamMemberPriceListEventOutput, error)
}

// ProcessTeamMemberPriceListEventInput contains data for processing team member price list events
type ProcessTeamMemberPriceListEventInput struct {
	MessageBody string
	EventType   string
}

// ProcessTeamMemberPriceListEventOutput contains the result of processing team member price list events
type ProcessTeamMemberPriceListEventOutput struct {
	Success   bool
	Message   string
	Operation string
}

type processTeamMemberPriceListEventUsecase struct {
	teamMemberPriceListRepo              repository.TeamMemberPriceListRepo
	teamMemberPriceListValidationService service.TeamMemberPriceListValidationService
}

func NewProcessTeamMemberPriceListEventUsecase(
	teamMemberPriceListRepo repository.TeamMemberPriceListRepo,
	teamMemberPriceListValidationService service.TeamMemberPriceListValidationService,
) ProcessTeamMemberPriceListEventUsecase {
	return &processTeamMemberPriceListEventUsecase{
		teamMemberPriceListRepo:              teamMemberPriceListRepo,
		teamMemberPriceListValidationService: teamMemberPriceListValidationService,
	}
}

func (uc *processTeamMemberPriceListEventUsecase) Execute(ctx context.Context, input *ProcessTeamMemberPriceListEventInput) (*ProcessTeamMemberPriceListEventOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "ProcessTeamMemberPriceListEventUsecase"), vlog.F("method", "Execute"))

	if input == nil {
		return nil, core.NewBusinessError("input is required")
	}

	logger.Info("processing team member price list event",
		vlog.F("event_type", input.EventType),
		vlog.F("message_body_length", len(input.MessageBody)))

	// Parse the event message
	var eventMessage TeamMemberPriceListEventMessage
	if err := json.Unmarshal([]byte(input.MessageBody), &eventMessage); err != nil {
		logger.Error("failed to parse event message", vlog.F("error", err))
		return nil, core.NewBusinessError("invalid event message format: %v", err)
	}

	// Determine the operation based on event type and data
	switch input.EventType {
	case "team-member-added-price-list":
		return uc.processTeamMemberAdded(ctx, &eventMessage)
	case "team-member-removed-price-list":
		return uc.processTeamMemberRemoved(ctx, &eventMessage)
	default:
		logger.Error("unsupported event type", vlog.F("event_type", input.EventType))
		return nil, core.NewBusinessError("unsupported event type: %s", input.EventType)
	}
}

func (uc *processTeamMemberPriceListEventUsecase) processTeamMemberAdded(ctx context.Context, eventMessage *TeamMemberPriceListEventMessage) (*ProcessTeamMemberPriceListEventOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("operation", "processTeamMemberAdded"))

	// Validate the input
	validationInput := &service.TeamMemberAddedValidationInput{
		BranchID:  eventMessage.BranchID,
		AccountID: eventMessage.AccountID,
		RoleID:    eventMessage.Role.ID,
		Fields:    make([]service.FieldValidationInput, len(eventMessage.Fields)),
	}

	for i, field := range eventMessage.Fields {
		validationInput.Fields[i] = service.FieldValidationInput{
			ID:       field.ID,
			StatusID: field.StatusID,
		}
	}

	if err := uc.teamMemberPriceListValidationService.ValidateTeamMemberAddedInput(ctx, validationInput); err != nil {
		logger.Error("validation failed for team member added event", vlog.F("error", err))
		return nil, err
	}

	// Create and upsert branch team member fields
	for _, fieldData := range eventMessage.Fields {
		branchTeamMemberField, err := entity.NewBranchTeamMemberField(&entity.NewBranchTeamMemberFieldInput{
			BranchID:      eventMessage.BranchID,
			AccountID:     eventMessage.AccountID,
			FieldID:       fieldData.ID,
			FieldStatusID: fieldData.StatusID,
			Enabled:       true,
			CreatedAt:     &eventMessage.UpdatedAt,
			UpdatedAt:     &eventMessage.UpdatedAt,
		})
		if err != nil {
			logger.Error("failed to create branch team member field entity", vlog.F("error", err))
			return nil, core.NewBusinessError("failed to create branch team member field entity: %v", err)
		}

		if err := uc.teamMemberPriceListValidationService.ValidateBranchTeamMemberFieldBusinessRules(ctx, branchTeamMemberField); err != nil {
			logger.Error("business rules validation failed for branch team member field", vlog.F("error", err))
			return nil, err
		}

		if err := uc.teamMemberPriceListRepo.UpsertBranchTeamMemberField(ctx, branchTeamMemberField); err != nil {
			logger.Error("failed to upsert branch team member field", vlog.F("error", err))
			return nil, err
		}
	}

	logger.Info("team member added successfully",
		vlog.F("branch_id", eventMessage.BranchID),
		vlog.F("account_id", eventMessage.AccountID),
		vlog.F("role_id", eventMessage.Role.ID),
		vlog.F("fields_count", len(eventMessage.Fields)))

	return &ProcessTeamMemberPriceListEventOutput{
		Success:   true,
		Message:   fmt.Sprintf("Team member added successfully for branch %s and account %s", eventMessage.BranchID, eventMessage.AccountID),
		Operation: "TEAM_MEMBER_ADDED",
	}, nil
}

func (uc *processTeamMemberPriceListEventUsecase) processTeamMemberRemoved(ctx context.Context, eventMessage *TeamMemberPriceListEventMessage) (*ProcessTeamMemberPriceListEventOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("operation", "processTeamMemberRemoved"))

	// Validate the input
	validationInput := &service.TeamMemberRemovedValidationInput{
		BranchID:  eventMessage.BranchID,
		AccountID: eventMessage.AccountID,
	}

	if err := uc.teamMemberPriceListValidationService.ValidateTeamMemberRemovedInput(ctx, validationInput); err != nil {
		logger.Error("validation failed for team member removed event", vlog.F("error", err))
		return nil, err
	}

	if err := uc.teamMemberPriceListRepo.SoftDeleteBranchTeamMemberFields(ctx, eventMessage.BranchID, eventMessage.AccountID); err != nil {
		logger.Error("failed to soft delete branch team member fields", vlog.F("error", err))
		return nil, err
	}

	logger.Info("team member removed successfully",
		vlog.F("branch_id", eventMessage.BranchID),
		vlog.F("account_id", eventMessage.AccountID))

	return &ProcessTeamMemberPriceListEventOutput{
		Success:   true,
		Message:   fmt.Sprintf("Team member removed successfully for branch %s and account %s", eventMessage.BranchID, eventMessage.AccountID),
		Operation: "TEAM_MEMBER_REMOVED",
	}, nil
}
