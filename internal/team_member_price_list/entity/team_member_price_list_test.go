package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewBranchTeamMemberField(t *testing.T) {
	now := time.Now().UTC()

	tests := []struct {
		name        string
		input       *NewBranchTeamMemberFieldInput
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid input with all fields",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "550e8400-e29b-41d4-a716-************",
				AccountID:     "550e8400-e29b-41d4-a716-************",
				FieldID:       "550e8400-e29b-41d4-a716-************",
				FieldStatusID: "550e8400-e29b-41d4-a716-************",
				Enabled:       true,
				CreatedAt:     &now,
				UpdatedAt:     &now,
			},
			expectError: false,
		},
		{
			name: "Valid input without timestamp",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "550e8400-e29b-41d4-a716-************",
				AccountID:     "550e8400-e29b-41d4-a716-************",
				FieldID:       "550e8400-e29b-41d4-a716-************",
				FieldStatusID: "550e8400-e29b-41d4-a716-************",
				Enabled:       false,
			},
			expectError: false,
		},
		{
			name: "Invalid branch ID",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "invalid-uuid",
				AccountID:     "550e8400-e29b-41d4-a716-************",
				FieldID:       "550e8400-e29b-41d4-a716-************",
				FieldStatusID: "550e8400-e29b-41d4-a716-************",
				Enabled:       true,
			},
			expectError: true,
		},
		{
			name: "Invalid account ID",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "550e8400-e29b-41d4-a716-************",
				AccountID:     "invalid-uuid",
				FieldID:       "550e8400-e29b-41d4-a716-************",
				FieldStatusID: "550e8400-e29b-41d4-a716-************",
				Enabled:       true,
			},
			expectError: true,
		},
		{
			name: "Invalid field ID",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "550e8400-e29b-41d4-a716-************",
				AccountID:     "550e8400-e29b-41d4-a716-************",
				FieldID:       "invalid-uuid",
				FieldStatusID: "550e8400-e29b-41d4-a716-************",
				Enabled:       true,
			},
			expectError: true,
		},
		{
			name: "Invalid field status ID",
			input: &NewBranchTeamMemberFieldInput{
				BranchID:      "550e8400-e29b-41d4-a716-************",
				AccountID:     "550e8400-e29b-41d4-a716-************",
				FieldID:       "550e8400-e29b-41d4-a716-************",
				FieldStatusID: "invalid-uuid",
				Enabled:       true,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.input == nil {
				assert.Panics(t, func() {
					NewBranchTeamMemberField(tt.input)
				})
				return
			}

			result, err := NewBranchTeamMemberField(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)

				assert.NotEmpty(t, result.ID())
				assert.Equal(t, tt.input.BranchID, result.BranchID())
				assert.Equal(t, tt.input.AccountID, result.AccountID())
				assert.Equal(t, tt.input.FieldID, result.FieldID())
				assert.Equal(t, tt.input.FieldStatusID, result.FieldStatusID())
				assert.Equal(t, tt.input.Enabled, result.Enabled())
				assert.NotZero(t, result.CreatedAt())
				assert.NotZero(t, result.UpdatedAt())

				if tt.input.CreatedAt != nil {
					assert.Equal(t, *tt.input.CreatedAt, result.CreatedAt())
				}
				if tt.input.UpdatedAt != nil {
					assert.Equal(t, *tt.input.UpdatedAt, result.UpdatedAt())
				}
			}
		})
	}
}

func TestBranchTeamMemberField_NilInputPanic(t *testing.T) {
	assert.Panics(t, func() {
		NewBranchTeamMemberField(nil)
	})
}
