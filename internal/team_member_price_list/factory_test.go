package team_member_price_list

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "sqlmock")
	return sqlxDB, mock
}

func TestNewFactory(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLTeamMemberAddedPriceList:   "https://sqs.us-east-1.amazonaws.com/123456789/team-member-added-price-list-queue",
			SQSQueueURLTeamMemberRemovedPriceList: "https://sqs.us-east-1.amazonaws.com/123456789/team-member-removed-price-list-queue",
			Region:                                "us-east-1",
			AccessKeyID:                           "test-access-key",
			SecretAccessKey:                       "test-secret-key",
		},
	}

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	factory, err := NewFactory(db, cfg)
	require.NoError(t, err)
	require.NotNil(t, factory)

	// Verify all dependencies are created
	assert.NotNil(t, factory.TeamMemberPriceListValidationService)
	assert.NotNil(t, factory.TeamMemberPriceListRepo)
	assert.NotNil(t, factory.ProcessTeamMemberPriceListEventUsecase)
	assert.NotNil(t, factory.TeamMemberPriceListEventListener)
}

func TestNewFactory_MissingAddedQueueURL(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLTeamMemberAddedPriceList:   "",
			SQSQueueURLTeamMemberRemovedPriceList: "https://sqs.us-east-1.amazonaws.com/123456789/team-member-removed-price-list-queue",
			Region:                                "us-east-1",
			AccessKeyID:                           "test-access-key",
			SecretAccessKey:                       "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg)
	assert.Error(t, err)
	assert.Nil(t, factory)
	assert.Contains(t, err.Error(), "team member added price list queue URL is not configured")
}

func TestNewFactory_MissingRemovedQueueURL(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLTeamMemberAddedPriceList:   "https://sqs.us-east-1.amazonaws.com/123456789/team-member-added-price-list-queue",
			SQSQueueURLTeamMemberRemovedPriceList: "",
			Region:                                "us-east-1",
			AccessKeyID:                           "test-access-key",
			SecretAccessKey:                       "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg)
	assert.Error(t, err)
	assert.Nil(t, factory)
	assert.Contains(t, err.Error(), "team member removed price list queue URL is not configured")
}
