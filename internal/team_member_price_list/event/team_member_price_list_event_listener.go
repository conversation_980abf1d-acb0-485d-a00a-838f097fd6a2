package event

import (
	"context"
	"errors"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/usecase"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// TeamMemberPriceListEventListener handles team member price list events from SQS
type TeamMemberPriceListEventListener struct {
	addedSubscriber   *SQSSubscriber
	removedSubscriber *SQSSubscriber
	usecase           usecase.ProcessTeamMemberPriceListEventUsecase
}

// NewTeamMemberPriceListEventListener creates a new team member price list event listener
func NewTeamMemberPriceListEventListener(
	cfg config.Configuration,
	usecase usecase.ProcessTeamMemberPriceListEventUsecase,
) (*TeamMemberPriceListEventListener, error) {
	// Validate that team member added queue URL is configured
	if cfg.AWS.SQSQueueURLTeamMemberAddedPriceList == "" {
		return nil, core.NewBusinessError("team member added price list queue URL is not configured")
	}

	// Validate that team member removed queue URL is configured
	if cfg.AWS.SQSQueueURLTeamMemberRemovedPriceList == "" {
		return nil, core.NewBusinessError("team member removed price list queue URL is not configured")
	}

	// Create SQS subscriber for team member added events
	addedSubscriber, err := NewSQSSubscriber(SQSSubscriberConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLTeamMemberAddedPriceList,
		MaxMessages:     10,
		WaitTimeSeconds: 20,
	})
	if err != nil {
		return nil, core.NewBusinessError("failed to create team member added SQS subscriber: %v", err)
	}

	// Create SQS subscriber for team member removed events
	removedSubscriber, err := NewSQSSubscriber(SQSSubscriberConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLTeamMemberRemovedPriceList,
		MaxMessages:     10,
		WaitTimeSeconds: 20,
	})
	if err != nil {
		return nil, core.NewBusinessError("failed to create team member removed SQS subscriber: %v", err)
	}

	return &TeamMemberPriceListEventListener{
		addedSubscriber:   addedSubscriber,
		removedSubscriber: removedSubscriber,
		usecase:           usecase,
	}, nil
}

// Start begins listening for team member price list events
func (l *TeamMemberPriceListEventListener) Start(ctx context.Context) error {
	logger := vlog.FromContext(ctx).With(vlog.F("component", "TeamMemberPriceListEventListener"))

	// Start team member added subscriber
	if err := l.addedSubscriber.Subscribe(ctx, func(ctx context.Context, message *awspkg.SQSMessage) error {
		return l.handleTeamMemberAddedEvent(ctx, message)
	}); err != nil {
		logger.Error("failed to start team member added subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start team member added subscriber: %w", err)
	}

	// Start team member removed subscriber
	if err := l.removedSubscriber.Subscribe(ctx, func(ctx context.Context, message *awspkg.SQSMessage) error {
		return l.handleTeamMemberRemovedEvent(ctx, message)
	}); err != nil {
		logger.Error("failed to start team member removed subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start team member removed subscriber: %w", err)
	}

	logger.Info("team member price list event listener started successfully")
	return nil
}

// Stop gracefully stops the event listener
func (l *TeamMemberPriceListEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("component", "TeamMemberPriceListEventListener"))
	logger.Info("stopping team member price list event listener")

	// Stop the added subscriber
	if l.addedSubscriber != nil {
		if err := l.addedSubscriber.Stop(); err != nil {
			logger.Error("failed to stop added subscriber", vlog.F("error", err))
			return err
		}
	}

	// Stop the removed subscriber
	if l.removedSubscriber != nil {
		if err := l.removedSubscriber.Stop(); err != nil {
			logger.Error("failed to stop removed subscriber", vlog.F("error", err))
			return err
		}
	}

	logger.Info("team member price list event listener stopped successfully")
	return nil
}

func (l *TeamMemberPriceListEventListener) handleTeamMemberAddedEvent(ctx context.Context, message *awspkg.SQSMessage) error {
	logger := vlog.FromContext(ctx).With(
		vlog.F("component", "TeamMemberPriceListEventListener"),
		vlog.F("event_type", "team-member-added-price-list"),
		vlog.F("message_id", message.MessageID),
	)

	logger.Info("processing team member added event", vlog.F("message_body_length", len(message.Body)))

	// Process the event using the usecase
	output, err := l.usecase.Execute(ctx, &usecase.ProcessTeamMemberPriceListEventInput{
		MessageBody: message.Body,
		EventType:   "team-member-added-price-list",
	})

	if err != nil {
		// Check if it's a business error (should go to DLQ)
		var businessErr *core.BusinessError
		if errors.As(err, &businessErr) {
			logger.Error("business error processing team member added price list event - message will go to DLQ",
				vlog.F("error", err),
				vlog.F("message_id", message.MessageID))
			return err
		}

		logger.Error("failed to process team member added price list event", vlog.F("error", err))
		return err
	}

	logger.Info("team member added price list event processed successfully",
		vlog.F("operation", output.Operation),
		vlog.F("success", output.Success),
		vlog.F("message", output.Message))

	return nil
}

func (l *TeamMemberPriceListEventListener) handleTeamMemberRemovedEvent(ctx context.Context, message *awspkg.SQSMessage) error {
	logger := vlog.FromContext(ctx).With(
		vlog.F("component", "TeamMemberPriceListEventListener"),
		vlog.F("event_type", "team-member-removed-price-list"),
		vlog.F("message_id", message.MessageID),
	)

	logger.Info("processing team member removed price list event", vlog.F("message_body_length", len(message.Body)))

	// Process the event using the usecase
	output, err := l.usecase.Execute(ctx, &usecase.ProcessTeamMemberPriceListEventInput{
		MessageBody: message.Body,
		EventType:   "team-member-removed-price-list",
	})

	if err != nil {
		// Check if it's a business error (should go to DLQ)
		var businessErr *core.BusinessError
		if errors.As(err, &businessErr) {
			logger.Error("business error processing team member removed price list event - message will go to DLQ",
				vlog.F("error", err),
				vlog.F("message_id", message.MessageID))
			return err
		}

		logger.Error("failed to process team member removed price list event", vlog.F("error", err))
		return err
	}

	logger.Info("team member removed price list event processed successfully",
		vlog.F("operation", output.Operation),
		vlog.F("success", output.Success),
		vlog.F("message", output.Message))

	return nil
}
