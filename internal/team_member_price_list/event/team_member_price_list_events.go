package event

import (
	"time"
)

// TeamMemberPriceListRoleData represents the role information in the event
type TeamMemberPriceListRoleData struct {
	ID      string `json:"id"`
	Acronym string `json:"acronym"`
}

// TeamMemberPriceListFieldData represents the field information in the event
type TeamMemberPriceListFieldData struct {
	ID       string `json:"id"`
	StatusID string `json:"status_id"`
}

// TeamMemberPriceListEventMessage represents the event message structure for team member price list operations
type TeamMemberPriceListEventMessage struct {
	BranchID  string                         `json:"branch_id"`
	AccountID string                         `json:"account_id"`
	Role      TeamMemberPriceListRoleData    `json:"role"`
	Fields    []TeamMemberPriceListFieldData `json:"fields"`
	UpdatedAt time.Time                      `json:"updated_at"`
}
