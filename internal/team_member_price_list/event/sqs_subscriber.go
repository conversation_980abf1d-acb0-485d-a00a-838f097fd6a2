package event

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSSubscriberConfig contains the configuration for SQS subscriber
type SQSSubscriberConfig struct {
	Region          string
	AccessKeyID     string
	SecretAccessKey string
	QueueURL        string
	MaxMessages     int32
	WaitTimeSeconds int32
}

// SQSSubscriber handles SQS message subscription
type SQSSubscriber struct {
	sqsClient *awspkg.SQSClient
	config    SQSSubscriberConfig
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSSubscriber creates a new SQS subscriber
func NewSQSSubscriber(config SQSSubscriberConfig) (*SQSSubscriber, error) {
	logger := vlog.New().With(vlog.F("subscriber", "SQSSubscriber"), vlog.F("queue_url", config.QueueURL))

	return &SQSSubscriber{
		sqsClient: nil,
		config:    config,
		logger:    logger,
		stopChan:  make(chan struct{}),
	}, nil
}

// initializeSQSClient creates and connects the SQS client
func (s *SQSSubscriber) initializeSQSClient(ctx context.Context) error {
	sqsConfig := awspkg.SQSConfig{
		Region:          s.config.Region,
		AccessKeyID:     s.config.AccessKeyID,
		SecretAccessKey: s.config.SecretAccessKey,
		QueueURL:        s.config.QueueURL,
	}

	s.logger.Info("initializing SQS client with timeout")

	// Create timeout context for connection
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Create SQS client
	sqsClient, err := awspkg.NewSQSClient(sqsConfig)
	if err != nil {
		return fmt.Errorf("failed to create SQS client: %w", err)
	}

	// Connect to AWS SQS with timeout context
	_, err = sqsClient.Connect(timeoutCtx)
	if err != nil {
		return fmt.Errorf("failed to connect to AWS SQS: %w", err)
	}

	s.sqsClient = sqsClient
	s.logger.Info("SQS client initialized successfully")
	return nil
}

// Subscribe starts listening for messages and processes them with the provided handler
func (s *SQSSubscriber) Subscribe(ctx context.Context, handler func(ctx context.Context, message *awspkg.SQSMessage) error) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("price list SQS subscriber is already running")
	}

	s.logger.Info("starting price list SQS subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx, handler)

	return nil
}

// Stop stops the SQS subscriber
func (s *SQSSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping price list SQS subscriber")

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	if s.sqsClient != nil {
		s.sqsClient.Close()
	}

	s.logger.Info("price list SQS subscriber stopped")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSSubscriber) pollMessages(ctx context.Context, handler func(ctx context.Context, message *awspkg.SQSMessage) error) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("context cancelled, stopping message polling")
			return
		case <-s.stopChan:
			s.logger.Info("stop signal received, stopping message polling")
			return
		case <-ticker.C:
			s.processMessages(ctx, handler)
		}
	}
}

// processMessages processes messages from SQS
func (s *SQSSubscriber) processMessages(ctx context.Context, handler func(ctx context.Context, message *awspkg.SQSMessage) error) {
	if s.sqsClient == nil {
		if err := s.initializeSQSClient(ctx); err != nil {
			s.logger.Error("failed to initialize SQS client", vlog.F("error", err))
			return
		}
	}

	result, err := s.sqsClient.ReceiveMessages(ctx, s.config.MaxMessages, s.config.WaitTimeSeconds)
	if err != nil {
		s.logger.Error("failed to receive messages from price list SQS", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	s.logger.Debug("received messages from price list SQS", vlog.F("count", len(result.Messages)))

	for _, message := range result.Messages {
		if err := s.processMessage(ctx, message, handler); err != nil {
			s.logger.Error("failed to process message", vlog.F("error", err), vlog.F("messageId", *message.MessageId))
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSSubscriber) processMessage(ctx context.Context, message types.Message, handler func(ctx context.Context, message *awspkg.SQSMessage) error) error {
	logger := s.logger.With(vlog.F("messageId", *message.MessageId))

	// Convert to our SQSMessage format
	sqsMessage := &awspkg.SQSMessage{
		MessageID: *message.MessageId,
		Body:      *message.Body,
	}

	// Process the message using the provided handler
	if err := handler(ctx, sqsMessage); err != nil {
		logger.Error("handler failed to process message", vlog.F("error", err))
		return err
	}

	// Delete the message from the queue after successful processing
	if err := s.deleteMessage(ctx, message); err != nil {
		logger.Error("failed to delete message from queue", vlog.F("error", err))
		return err
	}

	logger.Debug("message processed and deleted successfully")
	return nil
}

// deleteMessage deletes a message from the SQS queue
func (s *SQSSubscriber) deleteMessage(ctx context.Context, message types.Message) error {
	_, err := s.sqsClient.DeleteMessage(ctx, *message.ReceiptHandle)
	return err
}
