package event

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/usecase"
)

// Mock usecase
type MockProcessTeamMemberPriceListEventUsecase struct {
	mock.Mock
}

func (m *MockProcessTeamMemberPriceListEventUsecase) Execute(ctx context.Context, input *usecase.ProcessTeamMemberPriceListEventInput) (*usecase.ProcessTeamMemberPriceListEventOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.ProcessTeamMemberPriceListEventOutput), args.Error(1)
}

func TestNewTeamMemberPriceListEventListener_MissingConfig(t *testing.T) {
	mockUsecase := &MockProcessTeamMemberPriceListEventUsecase{}

	cfg := config.Configuration{}

	listener, err := NewTeamMemberPriceListEventListener(cfg, mockUsecase)
	assert.Error(t, err)
	assert.Nil(t, listener)
	assert.Contains(t, err.Error(), "team member added price list queue URL is not configured")
}
