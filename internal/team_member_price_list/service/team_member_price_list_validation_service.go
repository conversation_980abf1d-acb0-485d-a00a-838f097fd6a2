package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
)

// TeamMemberPriceListValidationService defines the interface for team member price list validation
type TeamMemberPriceListValidationService interface {
	ValidateTeamMemberAddedInput(ctx context.Context, input *TeamMemberAddedValidationInput) error
	ValidateTeamMemberRemovedInput(ctx context.Context, input *TeamMemberRemovedValidationInput) error
	ValidateBranchTeamMemberFieldBusinessRules(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error
}

// TeamMemberAddedValidationInput contains data for validating team member added operations
type TeamMemberAddedValidationInput struct {
	BranchID  string                 `validate:"required"`
	AccountID string                 `validate:"required"`
	RoleID    string                 `validate:"required"`
	Fields    []FieldValidationInput `validate:"required,dive"`
}

// TeamMemberRemovedValidationInput contains data for validating team member removed operations
type TeamMemberRemovedValidationInput struct {
	BranchID  string `validate:"required"`
	AccountID string `validate:"required"`
}

// FieldValidationInput contains data for validating field operations
type FieldValidationInput struct {
	ID       string `validate:"required"`
	StatusID string `validate:"required"`
}

type teamMemberPriceListValidationService struct{}

func NewTeamMemberPriceListValidationService() TeamMemberPriceListValidationService {
	return &teamMemberPriceListValidationService{}
}

func (s *teamMemberPriceListValidationService) ValidateTeamMemberAddedInput(ctx context.Context, input *TeamMemberAddedValidationInput) error {
	if input == nil {
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}
	if input.AccountID == "" {
		return core.NewBusinessError("account ID is required")
	}
	if input.RoleID == "" {
		return core.NewBusinessError("role ID is required")
	}
	if len(input.Fields) == 0 {
		return core.NewBusinessError("at least one field is required")
	}

	// Validate ID formats
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.AccountID); err != nil {
		return core.NewBusinessError("invalid account ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.RoleID); err != nil {
		return core.NewBusinessError("invalid role ID format: %v", err)
	}

	// Validate each field
	for i, field := range input.Fields {
		if field.ID == "" {
			return core.NewBusinessError("field ID at index %d is empty", i)
		}
		if field.StatusID == "" {
			return core.NewBusinessError("field status ID at index %d is empty", i)
		}
		if _, err := core.NewIDFromString(field.ID); err != nil {
			return core.NewBusinessError("invalid field ID format at index %d: %v", i, err)
		}
		if _, err := core.NewIDFromString(field.StatusID); err != nil {
			return core.NewBusinessError("invalid field status ID format at index %d: %v", i, err)
		}
	}

	return nil
}

func (s *teamMemberPriceListValidationService) ValidateTeamMemberRemovedInput(ctx context.Context, input *TeamMemberRemovedValidationInput) error {
	if input == nil {
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}
	if input.AccountID == "" {
		return core.NewBusinessError("account ID is required")
	}

	// Validate ID formats
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.AccountID); err != nil {
		return core.NewBusinessError("invalid account ID format: %v", err)
	}

	return nil
}

func (s *teamMemberPriceListValidationService) ValidateBranchTeamMemberFieldBusinessRules(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error {
	if branchTeamMemberField == nil {
		return core.NewBusinessError("branch team member field is required")
	}

	return nil
}
