package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
)

func TestTeamMemberPriceListValidationService_ValidateTeamMemberAddedInput(t *testing.T) {
	service := NewTeamMemberPriceListValidationService()
	ctx := context.Background()

	tests := []struct {
		name        string
		input       *TeamMemberAddedValidationInput
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid input",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: false,
		},
		{
			name:        "Nil input",
			input:       nil,
			expectError: true,
			errorMsg:    "validation input is required",
		},
		{
			name: "Empty branch ID",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "branch ID is required",
		},
		{
			name: "Empty account ID",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "account ID is required",
		},
		{
			name: "Empty role ID",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "role ID is required",
		},
		{
			name: "Empty fields",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields:    []FieldValidationInput{},
			},
			expectError: true,
			errorMsg:    "at least one field is required",
		},
		{
			name: "Invalid branch ID format",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "invalid-uuid",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid branch ID format",
		},
		{
			name: "Invalid account ID format",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "invalid-uuid",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid account ID format",
		},
		{
			name: "Invalid role ID format",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "invalid-uuid",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid role ID format",
		},
		{
			name: "Empty field ID",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "field ID at index 0 is empty",
		},
		{
			name: "Empty field status ID",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "",
					},
				},
			},
			expectError: true,
			errorMsg:    "field status ID at index 0 is empty",
		},
		{
			name: "Invalid field ID format",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "invalid-uuid",
						StatusID: "550e8400-e29b-41d4-a716-************",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid field ID format at index 0",
		},
		{
			name: "Invalid field status ID format",
			input: &TeamMemberAddedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
				RoleID:    "550e8400-e29b-41d4-a716-************",
				Fields: []FieldValidationInput{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						StatusID: "invalid-uuid",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid field status ID format at index 0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateTeamMemberAddedInput(ctx, tt.input)

			if tt.expectError {
				require.Error(t, err)
				var businessErr *core.BusinessError
				assert.True(t, errors.As(err, &businessErr))
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTeamMemberPriceListValidationService_ValidateTeamMemberRemovedInput(t *testing.T) {
	service := NewTeamMemberPriceListValidationService()
	ctx := context.Background()

	tests := []struct {
		name        string
		input       *TeamMemberRemovedValidationInput
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid input",
			input: &TeamMemberRemovedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "550e8400-e29b-41d4-a716-************",
			},
			expectError: false,
		},
		{
			name:        "Nil input",
			input:       nil,
			expectError: true,
			errorMsg:    "validation input is required",
		},
		{
			name: "Empty branch ID",
			input: &TeamMemberRemovedValidationInput{
				BranchID:  "",
				AccountID: "550e8400-e29b-41d4-a716-************",
			},
			expectError: true,
			errorMsg:    "branch ID is required",
		},
		{
			name: "Empty account ID",
			input: &TeamMemberRemovedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "",
			},
			expectError: true,
			errorMsg:    "account ID is required",
		},
		{
			name: "Invalid branch ID format",
			input: &TeamMemberRemovedValidationInput{
				BranchID:  "invalid-uuid",
				AccountID: "550e8400-e29b-41d4-a716-************",
			},
			expectError: true,
			errorMsg:    "invalid branch ID format",
		},
		{
			name: "Invalid account ID format",
			input: &TeamMemberRemovedValidationInput{
				BranchID:  "550e8400-e29b-41d4-a716-************",
				AccountID: "invalid-uuid",
			},
			expectError: true,
			errorMsg:    "invalid account ID format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateTeamMemberRemovedInput(ctx, tt.input)

			if tt.expectError {
				require.Error(t, err)
				var businessErr *core.BusinessError
				assert.True(t, errors.As(err, &businessErr))
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTeamMemberPriceListValidationService_ValidateBranchTeamMemberFieldBusinessRules(t *testing.T) {
	service := NewTeamMemberPriceListValidationService()
	ctx := context.Background()

	t.Run("Valid entity", func(t *testing.T) {
		branchTeamMemberField, err := entity.NewBranchTeamMemberField(&entity.NewBranchTeamMemberFieldInput{
			BranchID:      "550e8400-e29b-41d4-a716-************",
			AccountID:     "550e8400-e29b-41d4-a716-************",
			FieldID:       "550e8400-e29b-41d4-a716-************",
			FieldStatusID: "550e8400-e29b-41d4-a716-************",
			Enabled:       true,
		})
		require.NoError(t, err)

		err = service.ValidateBranchTeamMemberFieldBusinessRules(ctx, branchTeamMemberField)
		assert.NoError(t, err)
	})

	t.Run("Nil entity", func(t *testing.T) {
		err := service.ValidateBranchTeamMemberFieldBusinessRules(ctx, nil)
		require.Error(t, err)
		var businessErr *core.BusinessError
		assert.True(t, errors.As(err, &businessErr))
		assert.Contains(t, err.Error(), "branch team member field is required")
	})
}
