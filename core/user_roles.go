package core

import "slices"

const (
	RoleSuperAdmin  string = "super-admin"
	RoleBranchOwner string = "owner"
	RoleMember      string = "member"
)

type UserRoles struct {
	value []string
}

func NewUserRoles(r ...string) *UserRoles {
	return &UserRoles{
		value: r,
	}
}

func (ur UserRoles) HasAny(required ...string) bool {
	for _, r := range required {
		if slices.Contains(ur.value, r) {
			return true
		}
	}
	return false
}

func (ur UserRoles) Value() []string {
	out := make([]string, len(ur.value))
	copy(out, ur.value)
	return out
}

func (ur UserRoles) HasSingleRole(role string) bool {
	return slices.Contains(ur.value, role)
}

func HasAnyRole(have string, required ...string) bool {
	for _, r := range required {
		if have == r {
			return true
		}
	}

	return false
}
