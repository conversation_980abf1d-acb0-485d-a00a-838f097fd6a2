// Code generated by mockery. DO NOT EDIT.

package team_member_price_list

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	entity "gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
)

// MockTeamMemberPriceListRepo is an autogenerated mock type for the TeamMemberPriceListRepo type
type MockTeamMemberPriceListRepo struct {
	mock.Mock
}

// SoftDeleteBranchTeamMemberFields provides a mock function with given fields: ctx, branchID, accountID
func (_m *MockTeamMemberPriceListRepo) SoftDeleteBranchTeamMemberFields(ctx context.Context, branchID string, accountID string) error {
	ret := _m.Called(ctx, branchID, accountID)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, branchID, accountID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpsertBranchTeamMemberField provides a mock function with given fields: ctx, branchTeamMemberField
func (_m *MockTeamMemberPriceListRepo) UpsertBranchTeamMemberField(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error {
	ret := _m.Called(ctx, branchTeamMemberField)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.BranchTeamMemberField) error); ok {
		r0 = rf(ctx, branchTeamMemberField)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockTeamMemberPriceListRepo creates a new instance of MockTeamMemberPriceListRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockTeamMemberPriceListRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockTeamMemberPriceListRepo {
	mock := &MockTeamMemberPriceListRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
