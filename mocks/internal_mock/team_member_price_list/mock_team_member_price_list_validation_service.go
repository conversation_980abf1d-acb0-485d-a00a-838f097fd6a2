// Code generated by mockery. DO NOT EDIT.

package team_member_price_list

import (
	context "context"

	entity "gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/entity"
	mock "github.com/stretchr/testify/mock"
	service "gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/service"
)

// MockTeamMemberPriceListValidationService is an autogenerated mock type for the TeamMemberPriceListValidationService type
type MockTeamMemberPriceListValidationService struct {
	mock.Mock
}

// ValidateBranchTeamMemberFieldBusinessRules provides a mock function with given fields: ctx, branchTeamMemberField
func (_m *MockTeamMemberPriceListValidationService) ValidateBranchTeamMemberFieldBusinessRules(ctx context.Context, branchTeamMemberField *entity.BranchTeamMemberField) error {
	ret := _m.Called(ctx, branchTeamMemberField)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.BranchTeamMemberField) error); ok {
		r0 = rf(ctx, branchTeamMemberField)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ValidateTeamMemberAddedInput provides a mock function with given fields: ctx, input
func (_m *MockTeamMemberPriceListValidationService) ValidateTeamMemberAddedInput(ctx context.Context, input *service.TeamMemberAddedValidationInput) error {
	ret := _m.Called(ctx, input)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *service.TeamMemberAddedValidationInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ValidateTeamMemberRemovedInput provides a mock function with given fields: ctx, input
func (_m *MockTeamMemberPriceListValidationService) ValidateTeamMemberRemovedInput(ctx context.Context, input *service.TeamMemberRemovedValidationInput) error {
	ret := _m.Called(ctx, input)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *service.TeamMemberRemovedValidationInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockTeamMemberPriceListValidationService creates a new instance of MockTeamMemberPriceListValidationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockTeamMemberPriceListValidationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockTeamMemberPriceListValidationService {
	mock := &MockTeamMemberPriceListValidationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
