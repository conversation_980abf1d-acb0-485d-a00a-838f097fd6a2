// Code generated by mockery. DO NOT EDIT.

package team_member_price_list

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	usecase "gitlab.viswalslab.com/backend/price-list/internal/team_member_price_list/usecase"
)

// MockProcessTeamMemberPriceListEventUsecase is an autogenerated mock type for the ProcessTeamMemberPriceListEventUsecase type
type MockProcessTeamMemberPriceListEventUsecase struct {
	mock.Mock
}

// Execute provides a mock function with given fields: ctx, input
func (_m *MockProcessTeamMemberPriceListEventUsecase) Execute(ctx context.Context, input *usecase.ProcessTeamMemberPriceListEventInput) (*usecase.ProcessTeamMemberPriceListEventOutput, error) {
	ret := _m.Called(ctx, input)

	var r0 *usecase.ProcessTeamMemberPriceListEventOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *usecase.ProcessTeamMemberPriceListEventInput) (*usecase.ProcessTeamMemberPriceListEventOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *usecase.ProcessTeamMemberPriceListEventInput) *usecase.ProcessTeamMemberPriceListEventOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usecase.ProcessTeamMemberPriceListEventOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *usecase.ProcessTeamMemberPriceListEventInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockProcessTeamMemberPriceListEventUsecase creates a new instance of MockProcessTeamMemberPriceListEventUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockProcessTeamMemberPriceListEventUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProcessTeamMemberPriceListEventUsecase {
	mock := &MockProcessTeamMemberPriceListEventUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
