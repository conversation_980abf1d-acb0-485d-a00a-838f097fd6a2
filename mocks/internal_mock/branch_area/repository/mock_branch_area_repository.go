package repository_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/repository"
)

// MockBranchAreaRepo is a mock implementation of BranchAreaRepo
type MockBranchAreaRepo struct {
	mock.Mock
}

func (m *MockBranchAreaRepo) UpsertBranchArea(ctx context.Context, branchArea *entity.BranchArea) error {
	args := m.Called(ctx, branchArea)
	return args.Error(0)
}

func (m *MockBranchAreaRepo) UpsertBranchTeamMemberRole(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error {
	args := m.Called(ctx, branchTeamMemberRole)
	return args.Error(0)
}

// Ensure MockBranchAreaRepo implements repository.BranchAreaRepo
var _ repository.BranchAreaRepo = (*MockBranchAreaRepo)(nil)
