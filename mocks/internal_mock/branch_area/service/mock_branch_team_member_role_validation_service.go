package service_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/service"
)

// MockBranchTeamMemberRoleValidationService is a mock implementation of BranchTeamMemberRoleValidationService
type MockBranchTeamMemberRoleValidationService struct {
	mock.Mock
}

func (m *MockBranchTeamMemberRoleValidationService) ValidateBranchTeamMemberRoleUpsertInput(ctx context.Context, input *service.BranchTeamMemberRoleUpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockBranchTeamMemberRoleValidationService) ValidateBranchTeamMemberRoleBusinessRules(ctx context.Context, branchTeamMemberRole *entity.BranchTeamMemberRole) error {
	args := m.Called(ctx, branchTeamMemberRole)
	return args.Error(0)
}

// Ensure MockBranchTeamMemberRoleValidationService implements the interface
var _ service.BranchTeamMemberRoleValidationService = (*MockBranchTeamMemberRoleValidationService)(nil)
