package service_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/service"
)

// MockBranchAreaValidationService is a mock implementation of BranchAreaValidationService
type MockBranchAreaValidationService struct {
	mock.Mock
}

func (m *MockBranchAreaValidationService) ValidateBranchAreaBusinessRules(ctx context.Context, branchArea *entity.BranchArea) error {
	args := m.Called(ctx, branchArea)
	return args.Error(0)
}

func (m *MockBranchAreaValidationService) ValidateBranchAreaUpsertInput(ctx context.Context, input *service.BranchAreaUpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}
