package usecase_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_area/usecase"
)

// MockProcessBranchAreaEventUsecase is a mock implementation of ProcessBranchAreaEventUsecase
type MockProcessBranchAreaEventUsecase struct {
	mock.Mock
}

func (m *MockProcessBranchAreaEventUsecase) Execute(ctx context.Context, input *usecase.ProcessBranchAreaEventInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

// Ensure MockProcessBranchAreaEventUsecase implements the interface
var _ interface {
	Execute(ctx context.Context, input *usecase.ProcessBranchAreaEventInput) error
} = (*MockProcessBranchAreaEventUsecase)(nil)
