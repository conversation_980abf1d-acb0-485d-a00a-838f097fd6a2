---
type: "always_apply"
---

1. always check the core foldr for default functions like time, email, query, pagination and so on
2. before using func like fmt.Errorf() check for core.NewBusinessError()
3. for logger use vlog.logger
4. while creating a struct for json use validate wherever applicable like 
        AppointmentType         string                   `json:"appointment_type" validate:"required"` 
    instead of 
        AppointmentType         string                   `json:"appointment_type"`
