package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// BranchAreaEventMessage represents the event message structure for branch area operations
type BranchAreaEventMessage struct {
	ID                      string    `json:"id"`                        // branch ID
	Areas                   []string  `json:"areas"`                     // array of area IDs
	RepresentativeAccountID string    `json:"representative_account_id"` // account ID of the representative
	RepresentativeRole      string    `json:"representative_role"`       // role of the representative
	UpdatedAt               time.Time `json:"updated_at"`                // timestamp of the update
}

// Use REAL area UUIDs from database
const (
	area1ID = "84ce4d70-a442-412b-bf27-06f4544a8661" // Medicine
	area2ID = "4beed17b-a38a-4da1-8b26-94d2f1513001" // Dentistry
	area3ID = "e117dcf1-4acd-499f-80d2-7c868f23d6d0" // Psychology
)

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message BranchAreaEventMessage, description string) error {
	log.Printf("%s: Branch %s with %d areas, representative %s",
		description,
		message.ID[:8]+"...",
		len(message.Areas),
		message.RepresentativeAccountID[:8]+"...")

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "branch-area",
		"source":     "test-provider",
		"event_type": "branch-area.update",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("🚀 Branch Area Event Test Provider - Comprehensive")
	log.Println("=================================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Branch Area queue config
	if cfg.AWS.SQSQueueURLBranchArea == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_BRANCH_AREA not configured in .env")
	}

	log.Printf("Using Branch Area SQS Queue: %s", cfg.AWS.SQSQueueURLBranchArea)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLBranchArea,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()

	// Generate test UUIDs for accounts and branches
	representativeAccountID1 := uuid.New().String()
	representativeAccountID2 := uuid.New().String()
	branchID1 := uuid.New().String()
	branchID2 := uuid.New().String()

	log.Printf("📋 Test Data:")
	log.Printf("   Representative Account ID 1: %s", representativeAccountID1)
	log.Printf("   Representative Account ID 2: %s", representativeAccountID2)
	log.Printf("   Branch ID 1: %s", branchID1)
	log.Printf("   Branch ID 2: %s", branchID2)
	log.Printf("   Area ID 1 (Medicine): %s", area1ID)
	log.Printf("   Area ID 2 (Dentistry): %s", area2ID)
	log.Printf("   Area ID 3 (Psychology): %s", area3ID)
	log.Println()

	// Test scenarios
	scenarios := []struct {
		name        string
		message     BranchAreaEventMessage
		description string
	}{
		{
			name: "INSERT - New Branch Area Mapping",
			message: BranchAreaEventMessage{
				ID:                      branchID1,
				Areas:                   []string{area1ID}, // Medicine area
				RepresentativeAccountID: representativeAccountID1,
				RepresentativeRole:      "branch_manager",
				UpdatedAt:               time.Now().UTC(),
			},
			description: "Creates new branch area mapping with Medicine",
		},
		{
			name: "UPDATE - Modify Existing Mapping",
			message: BranchAreaEventMessage{
				ID:                      branchID1,
				Areas:                   []string{area1ID, area2ID}, // Medicine + Dentistry
				RepresentativeAccountID: representativeAccountID1,
				RepresentativeRole:      "branch_manager",
				UpdatedAt:               time.Now().UTC().Add(1 * time.Minute),
			},
			description: "Updates existing mapping with additional area (Dentistry)",
		},
		{
			name: "IGNORED - Older Event",
			message: BranchAreaEventMessage{
				ID:                      branchID1,
				Areas:                   []string{area1ID},
				RepresentativeAccountID: representativeAccountID1,
				RepresentativeRole:      "branch_manager",
				UpdatedAt:               time.Now().UTC().Add(-1 * time.Hour),
			},
			description: "Should be ignored due to older timestamp",
		},
		{
			name: "SECOND_BRANCH - Different Branch Mapping",
			message: BranchAreaEventMessage{
				ID:                      branchID2,
				Areas:                   []string{area3ID}, // Psychology
				RepresentativeAccountID: representativeAccountID2,
				RepresentativeRole:      "area_supervisor",
				UpdatedAt:               time.Now().UTC().Add(2 * time.Minute),
			},
			description: "Creates second branch mapping with Psychology area",
		},
		{
			name: "DLQ_TEST - Invalid Branch ID",
			message: BranchAreaEventMessage{
				ID:                      "invalid-uuid",
				Areas:                   []string{area1ID},
				RepresentativeAccountID: representativeAccountID1,
				RepresentativeRole:      "branch_manager",
				UpdatedAt:               time.Now().UTC(),
			},
			description: "Should go to DLQ due to invalid branch ID",
		},
		{
			name: "DLQ_TEST - Empty Areas",
			message: BranchAreaEventMessage{
				ID:                      branchID1,
				Areas:                   []string{}, // Empty areas
				RepresentativeAccountID: representativeAccountID1,
				RepresentativeRole:      "branch_manager",
				UpdatedAt:               time.Now().UTC(),
			},
			description: "Should go to DLQ due to empty areas",
		},
	}

	// Send test messages
	for i, scenario := range scenarios {
		log.Printf("📤 Test %d: %s", i+1, scenario.name)
		log.Printf("   Description: %s", scenario.description)

		err := sendMessage(ctx, sqsClient, scenario.message, scenario.name)
		if err != nil {
			log.Printf("❌ Failed to send message: %v", err)
		}

		log.Println()
		time.Sleep(1 * time.Second) // Small delay between messages
	}

	log.Println("🎯 Expected Results:")
	log.Println("   1. INSERT: New branch mapping created with Medicine area")
	log.Println("   2. UPDATE: Branch mapping updated with Medicine + Dentistry areas")
	log.Println("   3. IGNORED: Event discarded (older timestamp)")
	log.Println("   4. SECOND_BRANCH: Second branch mapping created with Psychology area")
	log.Println("   5. DLQ_TEST: Message sent to DLQ (invalid branch ID)")
	log.Println("   6. DLQ_TEST: Message sent to DLQ (empty areas)")
	log.Println()
	log.Println("✅ All test messages sent! Check your logs and database.")
}
