package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// Test scenarios for appointment type events
var testScenarios = []struct {
	name        string
	description string
	message     event.AppointmentTypeEventMessage
}{
	{
		name:        "Insert New Appointment Type",
		description: "Insert a new appointment type with auto-generated ID and timestamps",
		message: event.AppointmentTypeEventMessage{
			AppointmentType: "Consultation",
			Enabled:         true,
			CreatedAt:       core.ToPtr(time.Now().UTC()),
			UpdatedAt:       core.ToPtr(time.Now().UTC()),
		},
	},
	{
		name:        "Insert Appointment Type with Specific ID",
		description: "Insert a new appointment type with a specific UUID and timestamps",
		message: event.AppointmentTypeEventMessage{
			ID:              core.ToPtr(core.NewID().Value()),
			AppointmentType: "Follow-up",
			Enabled:         true,
			CreatedAt:       core.ToPtr(time.Now().UTC()),
			UpdatedAt:       core.ToPtr(time.Now().UTC()),
		},
	},
	{
		name:        "Update Existing Appointment Type",
		description: "Update an existing appointment type (disable it) with newer timestamp",
		message: event.AppointmentTypeEventMessage{
			ID:              core.ToPtr("84ce4d70-a442-412b-bf27-06f4544a8661"), // Use a known ID
			AppointmentType: "Emergency",
			Enabled:         false,
			CreatedAt:       core.ToPtr(time.Now().Add(-24 * time.Hour).UTC()), // Created yesterday
			UpdatedAt:       core.ToPtr(time.Now().UTC()),                      // Updated now
		},
	},
	{
		name:        "Insert Disabled Appointment Type",
		description: "Insert a new appointment type that is disabled with timestamps",
		message: event.AppointmentTypeEventMessage{
			AppointmentType: "Cancelled",
			Enabled:         false,
			CreatedAt:       core.ToPtr(time.Now().UTC()),
			UpdatedAt:       core.ToPtr(time.Now().UTC()),
		},
	},
	{
		name:        "Test Older Event (Should be Ignored)",
		description: "Send an event with older timestamp that should be ignored",
		message: event.AppointmentTypeEventMessage{
			ID:              core.ToPtr("84ce4d70-a442-412b-bf27-06f4544a8661"), // Same ID as above
			AppointmentType: "Old Emergency",
			Enabled:         true,
			CreatedAt:       core.ToPtr(time.Now().Add(-48 * time.Hour).UTC()), // Created 2 days ago
			UpdatedAt:       core.ToPtr(time.Now().Add(-2 * time.Hour).UTC()),  // Updated 2 hours ago (older than previous)
		},
	},
}

func main() {
	log.Println("🚀 Starting Appointment Type Test Provider - Enhanced with Timestamp Testing...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Appointment Type queue config
	if cfg.AWS.SQSQueueURLAppointmentType == "" {
		log.Fatal("❌ AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE not configured in .env")
	}

	log.Printf("📡 Using Appointment Type SQS Queue: %s", cfg.AWS.SQSQueueURLAppointmentType)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLAppointmentType,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("❌ Failed to initialize SQS client: %v", err)
	}

	ctx := context.Background()

	// Test connection
	log.Println("🔗 Testing SQS connection...")
	_, err = sqsClient.Connect(ctx)
	if err != nil {
		log.Fatalf("❌ Failed to connect to SQS: %v", err)
	}
	log.Println("✅ SQS connection successful")

	log.Println("\n📋 Test Scenarios Overview:")
	for i, scenario := range testScenarios {
		log.Printf("  %d. %s - %s", i+1, scenario.name, scenario.description)
	}

	// Execute test scenarios
	for i, scenario := range testScenarios {
		log.Printf("\n🧪 --- Test %d: %s ---", i+1, scenario.name)
		log.Printf("📝 Description: %s", scenario.description)

		// Log key details about the test
		if scenario.message.ID != nil {
			log.Printf("🆔 ID: %s", *scenario.message.ID)
		} else {
			log.Printf("🆔 ID: <auto-generated>")
		}
		log.Printf("📛 Name: %s", scenario.message.AppointmentType)
		log.Printf("🔘 Enabled: %t", scenario.message.Enabled)
		if scenario.message.CreatedAt != nil {
			log.Printf("📅 CreatedAt: %s", scenario.message.CreatedAt.Format(time.RFC3339))
		}
		if scenario.message.UpdatedAt != nil {
			log.Printf("🔄 UpdatedAt: %s", scenario.message.UpdatedAt.Format(time.RFC3339))
		}

		// Convert message to JSON
		messageBody, err := json.Marshal(scenario.message)
		if err != nil {
			log.Printf("❌ Failed to marshal message: %v", err)
			continue
		}

		log.Printf("📤 Sending message to SQS...")

		// Send message to SQS
		messageID, err := sqsClient.SendMessage(ctx, string(messageBody), nil)
		if err != nil {
			log.Printf("❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("✅ Message sent successfully with ID: %s", messageID)
		log.Printf("⏳ Waiting for processing...")

		// Wait a bit between messages to allow processing
		time.Sleep(3 * time.Second)
	}

	log.Println("\n🎉 All test scenarios completed!")
	log.Println("📊 Expected Results:")
	log.Println("  • Test 1: Should INSERT new 'Consultation' appointment type")
	log.Println("  • Test 2: Should INSERT new 'Follow-up' appointment type with specific ID")
	log.Println("  • Test 3: Should UPDATE existing 'Emergency' appointment type (disable it)")
	log.Println("  • Test 4: Should INSERT new 'Cancelled' appointment type (disabled)")
	log.Println("  • Test 5: Should be IGNORED (older timestamp than Test 3)")
	log.Println("\n🔍 Check your application logs to see:")
	log.Println("  • INSERT/UPDATE/IGNORED operations logged by the repository")
	log.Println("  • Event processing results from the usecase")
	log.Println("  • Any constraint violations or errors")
	log.Println("\n💾 Verify in your database:")
	log.Println("  • 4 appointment types should exist (Consultation, Follow-up, Emergency, Cancelled)")
	log.Println("  • Emergency should be disabled (from Test 3)")
	log.Println("  • Emergency should NOT show 'Old Emergency' name (Test 5 ignored)")
}
