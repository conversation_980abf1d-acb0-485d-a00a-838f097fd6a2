package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// NatureEventMessage represents the event message structure for nature operations
type NatureEventMessage struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Icon        string    `json:"icon"`
	Enabled     bool      `json:"enabled"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message NatureEventMessage, description string) error {
	log.Printf("🌿 %s: %s (ID: %s, Enabled: %v)",
		description,
		message.Name,
		message.ID,
		message.Enabled)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "nature",
		"source":     "test-provider",
		"event_type": "nature.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("🌿 Nature Test Provider - Comprehensive Testing")
	log.Println("==============================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Nature queue config
	if cfg.AWS.SQSQueueURLNature == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_NATURE not configured in .env")
	}

	log.Printf("Using Nature SQS Queue: %s", cfg.AWS.SQSQueueURLNature)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLNature,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()
	now := time.Now().UTC()

	// Test scenarios with comprehensive coverage
	scenarios := []struct {
		message     NatureEventMessage
		description string
		expected    string
	}{
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440001",
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
				CreatedAt:   now,
				UpdatedAt:   now,
			},
			description: "🆕 New Nature - INSERT",
			expected:    "✅ INSERT - New nature created",
		},
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440002",
				Name:        "Dermatology",
				Description: "Skin-related medical procedures",
				Icon:        "skin-icon.png",
				Enabled:     true,
				CreatedAt:   now,
				UpdatedAt:   now,
			},
			description: "🆕 Another New Nature - INSERT",
			expected:    "✅ INSERT - New nature created",
		},
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440001", // Same ID as first
				Name:        "Updated Cardiology",
				Description: "Updated heart-related procedures",
				Icon:        "updated-heart-icon.png",
				Enabled:     false, // Changed to disabled
				CreatedAt:   now.Add(-1 * time.Hour),
				UpdatedAt:   now.Add(5 * time.Minute), // Newer timestamp
			},
			description: "🔄 Update Existing Nature - UPDATE",
			expected:    "✅ UPDATE - Existing nature updated",
		},
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440001", // Same ID
				Name:        "Old Cardiology",
				Description: "Old description",
				Icon:        "old-icon.png",
				Enabled:     true,
				CreatedAt:   now.Add(-2 * time.Hour),
				UpdatedAt:   now.Add(-2 * time.Hour), // Much older timestamp
			},
			description: "⏰ Old Event - IGNORED",
			expected:    "⏰ IGNORED - Event older than existing record",
		},
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440003", // Different ID
				Name:        "Cardiology",                           // Same name as existing
				Description: "Duplicate name test",
				Icon:        "duplicate-icon.png",
				Enabled:     true,
				CreatedAt:   now,
				UpdatedAt:   now,
			},
			description: "🚫 Duplicate Name - DLQ",
			expected:    "🚫 DLQ - Duplicate name constraint violation",
		},
		{
			message: NatureEventMessage{
				ID:          "550e8400-e29b-41d4-a716-446655440004",
				Name:        "Neurology",
				Description: "Brain and nervous system procedures",
				Icon:        "brain-icon.png",
				Enabled:     true,
				CreatedAt:   now,
				UpdatedAt:   now,
			},
			description: "🆕 Neurology Nature - INSERT",
			expected:    "✅ INSERT - New nature created",
		},
	}

	log.Printf("📊 Total Test Scenarios: %d\n", len(scenarios))

	// Send all messages
	for i, scenario := range scenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(scenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendMessage(ctx, sqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("   ✅ Message sent successfully")
		log.Printf("   📋 Nature ID: %s", scenario.message.ID)
		log.Printf("   🏷️  Name: %s", scenario.message.Name)
		log.Printf("   ⚡ Enabled: %t", scenario.message.Enabled)
		log.Printf("   🕐 Updated At: %s\n", scenario.message.UpdatedAt.Format(time.RFC3339))

		// Wait between messages to ensure proper ordering
		time.Sleep(3 * time.Second)
	}

	log.Println("🎉 All test messages sent successfully!")
	log.Println("\n📋 Expected Results Summary:")
	log.Println("============================")
	for i, scenario := range scenarios {
		log.Printf("%d. %s → %s", i+1, scenario.description, scenario.expected)
	}

	log.Println("\n🔍 Monitor your service logs to see:")
	log.Println("• INSERT operations for new natures")
	log.Println("• UPDATE operations for existing natures with newer timestamps")
	log.Println("• IGNORED operations for events with older timestamps")
	log.Println("• DLQ routing for constraint violations")
	log.Println("• Clear operation logging with detailed information")
}
