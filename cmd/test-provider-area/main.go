package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// AreaEventMessage represents the event message structure
type AreaEventMessage struct {
	ID        string    `json:"id"`
	Label     string    `json:"label"`
	Acronym   string    `json:"acronym"`
	Enabled   bool      `json:"enabled"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"`
	EventType string    `json:"event_type"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message AreaEventMessage, description string) error {
	log.Printf("📨 %s: %s (%s)", description, message.Label, message.Acronym)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "area",
		"source":     "test-provider",
		"event_type": "area.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("🏢 Area Test Provider - Comprehensive Testing")
	log.Println("============================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Area queue config
	if cfg.AWS.SQSQueueURLArea == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_AREA not configured in .env")
	}

	log.Printf("Using Area SQS Queue: %s", cfg.AWS.SQSQueueURLArea)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLArea,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()
	now := time.Now().UTC()

	// Test scenarios with comprehensive coverage
	scenarios := []struct {
		message     AreaEventMessage
		description string
		expected    string
	}{
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440001",
				Label:     "North Region",
				Acronym:   "NORTH",
				Enabled:   true,
				CreatedAt: now,
				UpdatedAt: now,
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "🆕 New Area - INSERT",
			expected:    "✅ INSERT - New area created",
		},
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440002",
				Label:     "South Region",
				Acronym:   "SOUTH",
				Enabled:   true,
				CreatedAt: now,
				UpdatedAt: now,
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "🆕 Another New Area - INSERT",
			expected:    "✅ INSERT - New area created",
		},
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440001", // Same ID as first
				Label:     "North Region Updated",
				Acronym:   "NORTH",
				Enabled:   false, // Changed to disabled
				CreatedAt: now.Add(-1 * time.Hour),
				UpdatedAt: now.Add(5 * time.Minute), // Newer timestamp
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "🔄 Update Existing Area - UPDATE",
			expected:    "✅ UPDATE - Existing area updated",
		},
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440001", // Same ID
				Label:     "Old North Region",
				Acronym:   "NORTH",
				Enabled:   true,
				CreatedAt: now.Add(-2 * time.Hour),
				UpdatedAt: now.Add(-2 * time.Hour), // Much older timestamp
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "⏰ Old Event - IGNORED",
			expected:    "⏰ IGNORED - Event older than existing record",
		},
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440003", // Different ID
				Label:     "Another North Region",
				Acronym:   "NORTH", // Same acronym as existing
				Enabled:   true,
				CreatedAt: now,
				UpdatedAt: now,
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "🚫 Duplicate Acronym - DLQ",
			expected:    "🚫 DLQ - Duplicate acronym constraint violation",
		},
		{
			message: AreaEventMessage{
				ID:        "550e8400-e29b-41d4-a716-446655440004",
				Label:     "East Region",
				Acronym:   "EAST",
				Enabled:   true,
				CreatedAt: now,
				UpdatedAt: now,
				Timestamp: now,
				Source:    "test-provider",
				EventType: "area.upsert",
			},
			description: "🆕 East Region - INSERT",
			expected:    "✅ INSERT - New area created",
		},
	}

	log.Printf("📊 Total Test Scenarios: %d\n", len(scenarios))

	// Send all messages
	for i, scenario := range scenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(scenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendMessage(ctx, sqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("   ✅ Message sent successfully")
		log.Printf("   📋 Area ID: %s", scenario.message.ID)
		log.Printf("   🏷️  Label: %s", scenario.message.Label)
		log.Printf("   🔤 Acronym: %s", scenario.message.Acronym)
		log.Printf("   ⚡ Enabled: %t", scenario.message.Enabled)
		log.Printf("   🕐 Updated At: %s\n", scenario.message.UpdatedAt.Format(time.RFC3339))

		// Wait between messages to ensure proper ordering
		time.Sleep(3 * time.Second)
	}

	log.Println("🎉 All test messages sent successfully!")
	log.Println("\n📋 Expected Results Summary:")
	log.Println("============================")
	for i, scenario := range scenarios {
		log.Printf("%d. %s → %s", i+1, scenario.description, scenario.expected)
	}

	log.Println("\n🔍 Monitor your service logs to see:")
	log.Println("• INSERT operations for new areas")
	log.Println("• UPDATE operations for existing areas with newer timestamps")
	log.Println("• IGNORED operations for events with older timestamps")
	log.Println("• DLQ routing for constraint violations")
	log.Println("• Clear operation logging with detailed information")
}
