package main

import (
	"context"
	"os"

	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	echoMiddleware "github.com/labstack/echo/v4/middleware"
	_ "github.com/lib/pq"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/infra/health"
	"gitlab.viswalslab.com/backend/price-list/infra/server"
)

func main() {
	err := run()
	if err != nil {
		os.Exit(1)
	}
	os.Exit(0)
}

func run() error {
	var (
		ctx = context.Background()
		cfg = config.FromEnv()
	)

	logger := vlog.NewWithLevel(cfg.LogLevel)
	defer logger.Flush()

	logger = logger.With(
		vlog.F("application", cfg.AppName),
		vlog.F("environment", cfg.Environment))

	logger.Info("initializing price-list service")

	ctx = vlog.AttachLoggerToContext(ctx, logger)

	dsn := cfg.DataBase.URL
	db, err := sqlx.Open("postgres", dsn)
	if err != nil {
		panic("failed to open database connection: " + err.Error())
	}
	if err := db.PingContext(ctx); err != nil {
		panic("failed to ping database: " + err.Error())
	}
	defer db.Close()
	logger.Info("database connection established")

	router := bootstrapRouter(cfg)
	baseGroup := router.Group("/price-list")
	mainGroup := baseGroup.Group("/v1")

	router.Use(echoMiddleware.Logger())
	router.Use(echoMiddleware.Recover())
	logger.Info("middleware configured")

	healthHandler := health.NewHealthChecker(db)
	baseGroup.GET("/health", healthHandler.Handle())
	logger.Info("health endpoint registered")

	app, err := server.NewBuilder().
		WithContext(ctx).
		WithConfiguration(cfg).
		WithDatabase(db).
		WithRouter(router).
		WithBaseGroup(mainGroup).
		WithLogger(logger).
		Build()
	if err != nil {
		logger.Error("failed to build application", vlog.F("error", err))
		return err
	}

	logger.Info("application built successfully")

	// Start the application
	app.Run()

	return nil
}

func bootstrapRouter(cfg config.Configuration) *echo.Echo {

	router := echo.New()
	router.Pre(echoMiddleware.RemoveTrailingSlash())
	// Apply security middleware
	server.BootstrapSecurity(router, cfg)

	return router
}
