package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// TeamMemberPriceListRoleData represents the role information in the event
type TeamMemberPriceListRoleData struct {
	ID      string `json:"id"`
	Acronym string `json:"acronym"`
}

// TeamMemberPriceListFieldData represents the field information in the event
type TeamMemberPriceListFieldData struct {
	ID       string `json:"id"`
	StatusID string `json:"status_id"`
}

// TeamMemberPriceListEventMessage represents the event message structure for team member price list operations
type TeamMemberPriceListEventMessage struct {
	BranchID  string                         `json:"branch_id"`
	AccountID string                         `json:"account_id"`
	Role      TeamMemberPriceListRoleData    `json:"role"`
	Fields    []TeamMemberPriceListFieldData `json:"fields"`
	UpdatedAt time.Time                      `json:"updated_at"`
	Timestamp time.Time                      `json:"timestamp"`
	Source    string                         `json:"source"`
	EventType string                         `json:"event_type"`
}

func sendAddedMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message TeamMemberPriceListEventMessage, description string) error {
	log.Printf("👥 %s: Branch %s, Account %s, Role %s (%d fields)",
		description,
		message.BranchID,
		message.AccountID,
		message.Role.Acronym,
		len(message.Fields))

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "team_member_price_list",
		"source":     "test-provider",
		"event_type": "team-member-added-price-list",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func sendRemovedMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message TeamMemberPriceListEventMessage, description string) error {
	log.Printf("👥 %s: Branch %s, Account %s",
		description,
		message.BranchID,
		message.AccountID)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "team_member_price_list",
		"source":     "test-provider",
		"event_type": "team-member-removed-price-list",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("👥 Team Member Price List Test Provider - Comprehensive Testing")
	log.Println("==============================================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Team Member Added queue config
	if cfg.AWS.SQSQueueURLTeamMemberAddedPriceList == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_TEAM_MEMBER_ADDED_PRICE_LIST not configured in .env")
	}

	// Validate AWS Team Member Removed queue config
	if cfg.AWS.SQSQueueURLTeamMemberRemovedPriceList == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_TEAM_MEMBER_REMOVED_PRICE_LIST not configured in .env")
	}

	log.Printf("Using Team Member Added SQS Queue: %s", cfg.AWS.SQSQueueURLTeamMemberAddedPriceList)
	log.Printf("Using Team Member Removed SQS Queue: %s", cfg.AWS.SQSQueueURLTeamMemberRemovedPriceList)

	// Initialize SQS clients
	addedSqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLTeamMemberAddedPriceList,
	}

	addedSqsClient, err := awspkg.InitializeSQSClient(addedSqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize Added SQS client: %v", err)
	}
	defer addedSqsClient.Close()

	removedSqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLTeamMemberRemovedPriceList,
	}

	removedSqsClient, err := awspkg.InitializeSQSClient(removedSqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize Removed SQS client: %v", err)
	}
	defer removedSqsClient.Close()

	ctx := context.Background()
	now := time.Now().UTC()

	// Test scenarios with comprehensive coverage
	addedScenarios := []struct {
		message     TeamMemberPriceListEventMessage
		description string
		expected    string
	}{
		{
			message: TeamMemberPriceListEventMessage{
				BranchID:  "819e8a93-dffa-4bc1-9127-a6ad698fb1ad",
				AccountID: "4d8e5c9e-1b22-4dd7-99c0-9429ea651e89",
				Role: TeamMemberPriceListRoleData{
					ID:      "a116622e-9013-4ce9-a38e-10eb654de339",
					Acronym: "super-admin",
				},
				Fields: []TeamMemberPriceListFieldData{
					{
						ID:       "e8a5a947-d1c9-4399-b194-be55e91d431a",
						StatusID: "674cc22c-c8aa-4765-b4b7-c7102f60ef18",
					},
				},
				UpdatedAt: now,
			},
			description: "🆕 New Team Member Added - INSERT",
			expected:    "✅ INSERT - New team member added",
		},
		{
			message: TeamMemberPriceListEventMessage{
				BranchID:  "819e8a93-dffa-4bc1-9127-a6ad698fb1ad",
				AccountID: "550e8400-e29b-41d4-a716-************",
				Role: TeamMemberPriceListRoleData{
					ID:      "9b66379e-36f7-4a6f-ba15-dc35f168d45d",
					Acronym: "member",
				},
				Fields: []TeamMemberPriceListFieldData{
					{
						ID:       "e8a5a947-d1c9-4399-b194-be55e91d431a",
						StatusID: "674cc22c-c8aa-4765-b4b7-c7102f60ef18",
					},
					{
						ID:       "415e6d30-3297-4283-a37c-f958c3f3b174",
						StatusID: "d4e89055-71a7-4da4-bf4b-4277f6a234cf",
					},
				},
				UpdatedAt: now,
			},
			description: "🆕 Another Team Member Added - INSERT",
			expected:    "✅ INSERT - New team member added",
		},
	}

	removedScenarios := []struct {
		message     TeamMemberPriceListEventMessage
		description string
		expected    string
	}{
		{
			message: TeamMemberPriceListEventMessage{
				BranchID:  "819e8a93-dffa-4bc1-9127-a6ad698fb1ad",
				AccountID: "4d8e5c9e-1b22-4dd7-99c0-9429ea651e89",
				Role: TeamMemberPriceListRoleData{
					ID:      "",
					Acronym: "",
				},
				Fields:    nil,
				UpdatedAt: now,
			},
			description: "🗑️ Team Member Removed - SOFT DELETE",
			expected:    "✅ SOFT DELETE - Team member removed",
		},
	}

	log.Printf("📊 Total Test Scenarios: %d added + %d removed = %d\n", len(addedScenarios), len(removedScenarios), len(addedScenarios)+len(removedScenarios))

	// Send added messages
	log.Println("\n🆕 Sending Team Member Added Events:")
	log.Println("====================================")
	for i, scenario := range addedScenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(addedScenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendAddedMessage(ctx, addedSqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("   ✅ Message sent successfully")
		log.Printf("   📋 Branch ID: %s", scenario.message.BranchID)
		log.Printf("   👤 Account ID: %s", scenario.message.AccountID)
		log.Printf("   🏷️  Role: %s", scenario.message.Role.Acronym)
		log.Printf("   📊 Fields: %d", len(scenario.message.Fields))
		log.Printf("   🕐 Updated At: %s\n", scenario.message.UpdatedAt.Format(time.RFC3339))

		// Wait between messages to ensure proper ordering
		time.Sleep(3 * time.Second)
	}

	// Send removed messages
	log.Println("\n🗑️ Sending Team Member Removed Events:")
	log.Println("======================================")
	for i, scenario := range removedScenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(removedScenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendRemovedMessage(ctx, removedSqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("   ✅ Message sent successfully")
		log.Printf("   📋 Branch ID: %s", scenario.message.BranchID)
		log.Printf("   👤 Account ID: %s", scenario.message.AccountID)
		log.Printf("   🕐 Updated At: %s\n", scenario.message.UpdatedAt.Format(time.RFC3339))

		// Wait between messages to ensure proper ordering
		time.Sleep(3 * time.Second)
	}

	log.Println("🎉 All test messages sent successfully!")
	log.Println("\n📋 Expected Results Summary:")
	log.Println("============================")
	for i, scenario := range addedScenarios {
		log.Printf("%d. %s → %s", i+1, scenario.description, scenario.expected)
	}
	for i, scenario := range removedScenarios {
		log.Printf("%d. %s → %s", len(addedScenarios)+i+1, scenario.description, scenario.expected)
	}

	log.Println("\n🔍 Monitor your service logs to see:")
	log.Println("• INSERT operations for new team members")
	log.Println("• SOFT DELETE operations for removed team members")
	log.Println("• Field management logic for branch availability")
	log.Println("• Clear operation logging with detailed information")
}
