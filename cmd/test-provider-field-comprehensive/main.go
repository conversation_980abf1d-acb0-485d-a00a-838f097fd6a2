package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// FieldEventMessage represents the event message structure for field operations
type FieldEventMessage struct {
	ID          *string    `json:"id,omitempty"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	Icon        *string    `json:"icon,omitempty"`
	Position    *int       `json:"position,omitempty"`
	Enabled     bool       `json:"enabled"`
	AreaID      *string    `json:"area_id,omitempty"`
	CreatedAt   *time.Time `json:"created_at,omitempty"`
	UpdatedAt   *time.Time `json:"updated_at,omitempty"`
}

// validateFieldEvent validates the field event message
func validateFieldEvent(message FieldEventMessage) error {
	// Validate required fields
	if message.Name == "" {
		return errors.New("field name is required")
	}

	// Validate ID UUID format if provided
	if message.ID != nil && *message.ID != "" {
		if _, err := uuid.Parse(*message.ID); err != nil {
			return fmt.Errorf("invalid field ID UUID format: %v", err)
		}
	}

	// Validate AreaID UUID format if provided
	if message.AreaID != nil && *message.AreaID != "" {
		if _, err := uuid.Parse(*message.AreaID); err != nil {
			return fmt.Errorf("invalid area ID UUID format: %v", err)
		}
	}

	// Validate position if provided
	if message.Position != nil && *message.Position < 0 {
		return errors.New("position must be non-negative")
	}

	return nil
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message FieldEventMessage, description string) error {
	// Validate the message before sending
	if err := validateFieldEvent(message); err != nil {
		log.Printf("❌ VALIDATION FAILED: %s - %v", description, err)
		return fmt.Errorf("validation failed: %v", err)
	}

	log.Printf("📨 %s: Field '%s' (Enabled: %t)",
		description,
		message.Name,
		message.Enabled)

	// Log detailed field information
	log.Printf("   📋 Field Details:")
	log.Printf("      Name: %s", message.Name)
	log.Printf("      Enabled: %t", message.Enabled)
	if message.ID != nil {
		log.Printf("      ID: %s", *message.ID)
	}
	if message.Description != nil {
		log.Printf("      Description: %s", *message.Description)
	}
	if message.Icon != nil {
		log.Printf("      Icon: %s", *message.Icon)
	}
	if message.Position != nil {
		log.Printf("      Position: %d", *message.Position)
	}
	if message.AreaID != nil {
		log.Printf("      Area ID: %s", *message.AreaID)
	}

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		log.Printf("❌ JSON MARSHAL FAILED: %s - %v", description, err)
		return fmt.Errorf("json marshal failed: %v", err)
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "field",
		"source":     "test-provider",
		"event_type": "field.upsert",
	})

	if err != nil {
		log.Printf("❌ SQS SEND FAILED: %s - %v", description, err)
		return fmt.Errorf("sqs send failed: %v", err)
	}

	log.Printf("✅ SUCCESS: %s", description)
	return nil
}

func main() {
	log.Println("🏷️ Field Test Provider - Comprehensive Testing")
	log.Println("============================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Field queue config
	if cfg.AWS.SQSQueueURLField == "" {
		log.Fatal("❌ AWS_SQS_QUEUE_URL_FIELD not configured in .env")
	}

	log.Printf("Using Field SQS Queue: %s", cfg.AWS.SQSQueueURLField)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLField,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("❌ Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()
	now := time.Now().UTC()

	// Generate test UUIDs
	field1ID := uuid.New().String()
	field2ID := uuid.New().String()
	field3ID := uuid.New().String()

	// Use REAL area UUIDs from database
	area1ID := "84ce4d70-a442-412b-bf27-06f4544a8661" // Medicine
	area2ID := "4beed17b-a38a-4da1-8b26-94d2f1513001" // Dentistry

	// Test scenarios with comprehensive coverage
	scenarios := []struct {
		message     FieldEventMessage
		description string
		expected    string
		waitTime    time.Duration
	}{
		// Happy path scenarios
		{
			message: FieldEventMessage{
				ID:          &field1ID,
				Name:        "Consultation Fee",
				Description: stringPtr("Standard consultation fee for patients"),
				Icon:        stringPtr("💰"),
				Position:    intPtr(1),
				Enabled:     true,
				AreaID:      &area1ID,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🆕 Create New Field - INSERT",
			expected:    "✅ INSERT - New field created",
			waitTime:    8 * time.Second,
		},
		{
			message: FieldEventMessage{
				ID:          &field2ID,
				Name:        "Follow-up Visit",
				Description: stringPtr("Follow-up consultation fee"),
				Icon:        stringPtr("🔄"),
				Position:    intPtr(2),
				Enabled:     true,
				AreaID:      &area2ID,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🆕 Create Another Field - INSERT",
			expected:    "✅ INSERT - New field created",
			waitTime:    8 * time.Second,
		},
		{
			message: FieldEventMessage{
				ID:          &field1ID,
				Name:        "Consultation Fee Updated",
				Description: stringPtr("Updated consultation fee description"),
				Icon:        stringPtr("💰✨"),
				Position:    intPtr(3),
				Enabled:     false,
				AreaID:      &area2ID,
				CreatedAt:   &now,
				UpdatedAt:   timePtr(now.Add(1 * time.Minute)),
			},
			description: "🔄 Update Existing Field - UPDATE",
			expected:    "✅ UPDATE - Existing field updated",
			waitTime:    8 * time.Second,
		},
		{
			message: FieldEventMessage{
				ID:          &field3ID,
				Name:        "Emergency Fee",
				Description: nil,
				Icon:        nil,
				Position:    nil,
				Enabled:     true,
				AreaID:      nil,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🆕 Create Field with Minimal Data - INSERT",
			expected:    "✅ INSERT - New field created with minimal data",
			waitTime:    8 * time.Second,
		},

		// Error scenarios - These should fail validation
		{
			message: FieldEventMessage{
				ID:          stringPtr("invalid-uuid-format"),
				Name:        "",
				Description: stringPtr("Missing name"),
				Enabled:     true,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🚫 Missing Field Name - VALIDATION ERROR",
			expected:    "❌ VALIDATION FAILED - Field name is required",
			waitTime:    1 * time.Second,
		},
		{
			message: FieldEventMessage{
				ID:          stringPtr("invalid-uuid-format"),
				Name:        "Invalid ID Field",
				Description: stringPtr("Field with invalid UUID"),
				Enabled:     true,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🚫 Invalid Field ID UUID - VALIDATION ERROR",
			expected:    "❌ VALIDATION FAILED - Invalid field ID UUID format",
			waitTime:    1 * time.Second,
		},
		{
			message: FieldEventMessage{
				Name:        "Invalid Area Field",
				Description: stringPtr("Field with invalid area UUID"),
				AreaID:      stringPtr("invalid-area-uuid"),
				Enabled:     true,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🚫 Invalid Area ID UUID - VALIDATION ERROR",
			expected:    "❌ VALIDATION FAILED - Invalid area ID UUID format",
			waitTime:    1 * time.Second,
		},
		{
			message: FieldEventMessage{
				Name:        "Negative Position Field",
				Description: stringPtr("Field with negative position"),
				Position:    intPtr(-1),
				Enabled:     true,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			},
			description: "🚫 Negative Position - VALIDATION ERROR",
			expected:    "❌ VALIDATION FAILED - Position must be non-negative",
			waitTime:    1 * time.Second,
		},
	}

	log.Printf("📊 Total Test Scenarios: %d", len(scenarios))
	log.Printf("   ✅ %d Happy Path Scenarios", 4)
	log.Printf("   ❌ %d Error Scenarios", 4)
	log.Println("")

	// Send all test scenarios
	for i, scenario := range scenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(scenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendMessage(ctx, sqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed: %v", err)
		} else {
			log.Printf("   ✅ Message processed successfully")
		}

		// Log detailed information for successful scenarios
		if i < 4 { // Only for happy path scenarios
			log.Printf("   📋 Field: %s", scenario.message.Name)
			log.Printf("      Enabled: %t", scenario.message.Enabled)
			if scenario.message.ID != nil {
				log.Printf("      ID: %s", *scenario.message.ID)
			}
			if scenario.message.AreaID != nil {
				log.Printf("      Area: %s", *scenario.message.AreaID)
			}
			if scenario.message.Position != nil {
				log.Printf("      Position: %d", *scenario.message.Position)
			}
		}

		log.Printf("   ⏰ Waiting %v before next test...\n", scenario.waitTime)
		time.Sleep(scenario.waitTime)
	}

	log.Println("🎉 All test scenarios completed!")
	log.Println("\n📋 Expected Results Summary:")
	log.Println("============================")
	for i, scenario := range scenarios {
		status := "✅"
		if i >= 4 {
			status = "❌"
		}
		log.Printf("%s %d. %s → %s", status, i+1, scenario.description, scenario.expected)
	}

	log.Println("\n🔍 Monitor your service logs to see:")
	log.Println("• INSERT operations for new fields")
	log.Println("• UPDATE operations for existing fields with newer timestamps")
	log.Println("• VALIDATION ERRORS for invalid data formats")
	log.Println("• Clear operation logging with detailed information")

	log.Println("\n📊 Final Expected Database State:")
	log.Println("================================")
	log.Printf("Field 1 (%s): 'Consultation Fee Updated' - Enabled: false, Area: %s, Position: 3",
		field1ID[:8]+"...", area2ID[:8]+"...")
	log.Printf("Field 2 (%s): 'Follow-up Visit' - Enabled: true, Area: %s, Position: 2",
		field2ID[:8]+"...", area2ID[:8]+"...")
	log.Printf("Field 3 (%s): 'Emergency Fee' - Enabled: true, Area: null, Position: null",
		field3ID[:8]+"...")

	log.Println("\n💾 SQL Query to Verify:")
	log.Println("SELECT id, name, description, icon, position, enabled, area_id, created_at, updated_at")
	log.Println("FROM fields")
	log.Printf("WHERE id IN ('%s', '%s', '%s')", field1ID, field2ID, field3ID)
	log.Println("ORDER BY created_at;")
}

// Helper functions for creating pointers
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}

func timePtr(t time.Time) *time.Time {
	return &t
}
