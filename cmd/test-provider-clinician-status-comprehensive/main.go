package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// ClinicianStatusEventData represents the data structure for clinician status events
type ClinicianStatusEventData struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Enabled   bool      `json:"enabled"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ClinicianStatusEventMessage represents the event message structure for clinician status operations
type ClinicianStatusEventMessage struct {
	Data      ClinicianStatusEventData `json:"data"`
	Timestamp time.Time                `json:"timestamp"`
	Source    string                   `json:"source"`
	EventType string                   `json:"event_type"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message ClinicianStatusEventMessage, description string) error {
	log.Printf("📨 %s: %s (ID: %s, Enabled: %v)",
		description,
		message.Data.Name,
		message.Data.ID,
		message.Data.Enabled)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "clinician-status",
		"source":     "test-provider",
		"event_type": "clinician-status.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("🩺 Clinician Status Test Provider - Comprehensive Testing")
	log.Println("========================================================")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Clinician Status queue config
	if cfg.AWS.SQSQueueURLClinicianStatus == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_CLINICIAN_STATUS not configured in .env")
	}

	log.Printf("Using Clinician Status SQS Queue: %s", cfg.AWS.SQSQueueURLClinicianStatus)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLClinicianStatus,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()
	now := time.Now().UTC()

	// Test scenarios with comprehensive coverage
	scenarios := []struct {
		message     ClinicianStatusEventMessage
		description string
		expected    string
	}{
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440001",
					Name:      "Junior",
					Enabled:   true,
					CreatedAt: now,
					UpdatedAt: now,
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "🆕 New Clinician Status - INSERT",
			expected:    "✅ INSERT - New clinician status created",
		},
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440002",
					Name:      "Senior",
					Enabled:   true,
					CreatedAt: now,
					UpdatedAt: now,
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "🆕 Another New Status - INSERT",
			expected:    "✅ INSERT - New clinician status created",
		},
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440001", // Same ID as first
					Name:      "Junior Updated",
					Enabled:   false, // Changed to disabled
					CreatedAt: now.Add(-1 * time.Hour),
					UpdatedAt: now.Add(5 * time.Minute), // Newer timestamp
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "🔄 Update Existing Status - UPDATE",
			expected:    "✅ UPDATE - Existing clinician status updated",
		},
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440001", // Same ID
					Name:      "Old Junior",
					Enabled:   true,
					CreatedAt: now.Add(-2 * time.Hour),
					UpdatedAt: now.Add(-2 * time.Hour), // Much older timestamp
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "⏰ Old Event - IGNORED",
			expected:    "⏰ IGNORED - Event older than existing record",
		},
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440003", // Different ID
					Name:      "Senior",                               // Same name as existing
					Enabled:   true,
					CreatedAt: now,
					UpdatedAt: now,
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "🚫 Duplicate Name - DLQ",
			expected:    "🚫 DLQ - Duplicate name constraint violation",
		},
		{
			message: ClinicianStatusEventMessage{
				Data: ClinicianStatusEventData{
					ID:        "550e8400-e29b-41d4-a716-446655440004",
					Name:      "Resident",
					Enabled:   true,
					CreatedAt: now,
					UpdatedAt: now,
				},
				Timestamp: now,
				Source:    "test-provider",
				EventType: "clinician-status.upsert",
			},
			description: "🆕 Resident Status - INSERT",
			expected:    "✅ INSERT - New clinician status created",
		},
	}

	log.Printf("📊 Total Test Scenarios: %d\n", len(scenarios))

	// Send all messages
	for i, scenario := range scenarios {
		log.Printf("🧪 Test %d/%d: %s", i+1, len(scenarios), scenario.description)
		log.Printf("   🎯 Expected: %s", scenario.expected)

		if err := sendMessage(ctx, sqsClient, scenario.message, scenario.description); err != nil {
			log.Printf("   ❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("   ✅ Message sent successfully")
		log.Printf("   📋 Status ID: %s", scenario.message.Data.ID)
		log.Printf("   🏷️  Name: %s", scenario.message.Data.Name)
		log.Printf("   ⚡ Enabled: %t", scenario.message.Data.Enabled)
		log.Printf("   🕐 Updated At: %s\n", scenario.message.Data.UpdatedAt.Format(time.RFC3339))

		// Wait between messages to ensure proper ordering
		time.Sleep(3 * time.Second)
	}

	log.Println("🎉 All test messages sent successfully!")
	log.Println("\n📋 Expected Results Summary:")
	log.Println("============================")
	for i, scenario := range scenarios {
		log.Printf("%d. %s → %s", i+1, scenario.description, scenario.expected)
	}

	log.Println("\n🔍 Monitor your service logs to see:")
	log.Println("• INSERT operations for new clinician statuses")
	log.Println("• UPDATE operations for existing statuses with newer timestamps")
	log.Println("• IGNORED operations for events with older timestamps")
	log.Println("• DLQ routing for constraint violations")
	log.Println("• Clear operation logging with detailed information")
}
