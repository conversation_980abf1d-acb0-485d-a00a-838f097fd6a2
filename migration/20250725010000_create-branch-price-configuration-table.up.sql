-- Types for list discriminator and price type
CREATE TYPE price_list_type AS ENUM ('appointment', 'treatment');
CREATE TYPE price_type AS ENUM ('fixed', 'from');

CREATE TABLE branch_price_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL,
    field_id UUID NOT NULL,
    nature_id UUID NOT NULL,
    clinician_status VARCHAR(32) NOT NULL,
    prices INT NOT NULL,

    -- duration/appointment_type may be NULL for treatment
    duration INT,
    appointment_type VARCHAR(32),

    -- new columns
    price_list_type price_list_type NOT NULL DEFAULT 'appointment',
    price_type price_type NOT NULL DEFAULT 'fixed',

    created_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_date TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- unique per branch, field, nature, clinician_status, and price_list_type
    CONSTRAINT unique_branch_field_nature UNIQUE (branch_id, field_id, nature_id, clinician_status, price_list_type),

    -- treatment rows must not carry appointment-only fields
    CONSTRAINT chk_treatment_fields CHECK (
        (price_list_type = 'treatment' AND appointment_type = 'in_clinic')
        OR
        (price_list_type = 'appointment')
    ),

    CONSTRAINT fk_field FOREIGN KEY (field_id) REFERENCES field(uuid),
    CONSTRAINT fk_nature FOREIGN KEY (nature_id) REFERENCES nature(uuid)
);