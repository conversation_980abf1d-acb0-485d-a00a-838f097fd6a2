CREATE TABLE public.branch_team_member_role (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID NOT NULL,
  account_id UUID NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT unique_branch_account UNIQUE (branch_id, account_id)
);

-- Create index for better query performance
CREATE INDEX idx_branch_team_member_role_account_id ON public.branch_team_member_role(account_id);
CREATE INDEX idx_branch_team_member_role_branch_id ON public.branch_team_member_role(branch_id);
