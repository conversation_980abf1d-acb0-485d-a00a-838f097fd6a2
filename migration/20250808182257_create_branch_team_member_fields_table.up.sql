-- Create branch_team_member_fields table
CREATE TABLE public.branch_team_member_fields (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id uuid NOT NULL,
    account_id uuid NOT NULL,
    field_id uuid NOT NULL,
    field_status_id uuid NOT NULL,
    enabled bool NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT branch_team_member_fields_field_fk FOREIGN KEY (field_id) REFERENCES public.field(uuid) ON DELETE CASCADE,
    CONSTRAINT branch_team_member_fields_field_status_fk FOREIGN KEY (field_status_id) REFERENCES public.field_status(id) ON DELETE CASCADE,
    CONSTRAINT unique_branch_account_field_status UNIQUE (branch_id, account_id, field_id, field_status_id)
);

CREATE INDEX idx_branch_team_member_fields_branch_account ON public.branch_team_member_fields(branch_id, account_id);
CREATE INDEX idx_branch_team_member_fields_branch_field ON public.branch_team_member_fields(branch_id, field_id);